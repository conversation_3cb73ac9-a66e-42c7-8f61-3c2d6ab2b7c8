# SAST报告生成系统JPQL查询改进

## 概述

本文档描述了使用JPQL（Java Persistence Query Language）替代方法名查询和原生SQL查询的改进。JPQL是JPA规范的一部分，提供了面向对象的查询语言，使开发人员能够以面向对象的方式编写数据库查询。

## 主要改进

### 1. 在SastDefectRepo中使用@Query注解

替换了基于方法名的查询，使用@Query注解定义JPQL查询：

```java
@Query("SELECT d FROM SastDefect d WHERE d.taskId = :taskId AND d.delFlag = :delFlag ORDER BY d.defectLevel ASC")
List<SastDefect> findDefectsByTaskIdAndDelFlag(@Param("taskId") Long taskId, @Param("delFlag") Integer delFlag);
```

添加了更复杂的统计查询：

```java
@Query("SELECT " +
       "SUM(CASE WHEN d.defectLevel = 1 THEN 1 ELSE 0 END) AS highRisk, " +
       "SUM(CASE WHEN d.defectLevel = 2 THEN 1 ELSE 0 END) AS mediumRisk, " +
       "SUM(CASE WHEN d.defectLevel = 3 THEN 1 ELSE 0 END) AS lowRisk " +
       "FROM SastDefect d WHERE d.taskId = :taskId AND d.delFlag = 0")
Object[] countVulnerabilityStatsByTaskId(@Param("taskId") Long taskId);
```

### 2. 在SastPdfReportServiceImpl中使用JPQL查询

添加了使用JPQL查询获取漏洞详情的方法：

```java
private List<SastReportDefectModel> getTopDefects(Long taskId, int limit) {
    // 使用JPQL查询获取最严重的前N个漏洞
    List<SastDefect> topDefects = defectRepo.findTopDefectsByTaskId(taskId, limit);
    // ...
}
```

### 3. 在SastReportManagementServiceImpl中使用EntityManager直接执行JPQL

添加了使用EntityManager直接执行JPQL查询的方法：

```java
@SuppressWarnings("unchecked")
private Map<String, Object> getVulnerabilityStatsByJPQL(Long taskId) {
    Map<String, Object> result = new HashMap<>();
    
    try {
        // 统计各等级漏洞数量
        String jpql = "SELECT " +
                "SUM(CASE WHEN d.defectLevel = 1 THEN 1 ELSE 0 END) AS highRisk, " +
                "SUM(CASE WHEN d.defectLevel = 2 THEN 1 ELSE 0 END) AS mediumRisk, " +
                "SUM(CASE WHEN d.defectLevel = 3 THEN 1 ELSE 0 END) AS lowRisk " +
                "FROM SastDefect d WHERE d.taskId = :taskId AND d.delFlag = 0";
        
        Query query = entityManager.createQuery(jpql);
        query.setParameter("taskId", taskId);
        
        Object[] stats = (Object[]) query.getSingleResult();
        // ...
    } catch (Exception e) {
        log.error("获取漏洞统计数据失败: taskId={}, error={}", taskId, e.getMessage(), e);
    }
    
    return result;
}
```

### 4. 在SastReportTemplateController中添加使用JPQL的API

添加了两个使用JPQL查询的API：

1. `/template/report-details`：使用JPQL查询获取报告详情数据
2. `/template/defect-detail`：使用JPQL查询获取单个漏洞的详细信息

## 优势

1. **类型安全**：JPQL查询在编译时进行类型检查，减少运行时错误
2. **可移植性**：JPQL是JPA标准的一部分，不依赖于特定的数据库方言
3. **面向对象**：JPQL查询使用实体类和属性名，而不是表名和列名
4. **复杂查询**：可以编写复杂的查询，包括聚合函数、条件表达式等
5. **性能优化**：可以通过设置查询参数和结果限制来优化查询性能

## 使用场景

1. **简单查询**：使用Repository接口中的@Query注解
2. **动态查询**：使用EntityManager和JPQL字符串
3. **复杂聚合查询**：使用JPQL的聚合函数和CASE表达式
4. **原生SQL**：对于特别复杂的查询，仍然可以使用entityManager.createNativeQuery()

## 注意事项

1. 注意JPQL和SQL的语法差异
2. 使用命名参数（:paramName）而不是位置参数（?1）以提高可读性
3. 对于大量数据的查询，考虑分页和限制结果集大小
4. 对于复杂查询，考虑使用投影（SELECT NEW）来优化性能
5. 对于频繁执行的查询，考虑使用查询缓存 