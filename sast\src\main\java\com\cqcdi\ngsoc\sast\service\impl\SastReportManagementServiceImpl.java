/*
 * Copyright @ 2024 重庆市信息通信咨询设计院有限公司版权所有
 *
 * 项目名称: ngsoc-api
 * 文件名称: SastReportManagementServiceImpl.java
 *
 * 创建人: Weilq
 * 创建日期: 2025/7/31
 *
 * 版权描述:此软件未经重庆市信息通信咨询设计院有限公司许可，严禁发布、传播、使用
 * 公司地址:重庆市九龙坡区科园四路257号，400041.
 */
package com.cqcdi.ngsoc.sast.service.impl;

import cn.hutool.core.util.StrUtil;
import com.cqcdi.ngsoc.common.entity.common.dto.PageVo;
import com.cqcdi.ngsoc.common.entity.common.dto.R;
import com.cqcdi.ngsoc.common.entity.sast.SastReportManagement;
import com.cqcdi.ngsoc.common.entity.sast.SastProjectManagement;
import com.cqcdi.ngsoc.common.entity.sast.SastTaskManagement;
import com.cqcdi.ngsoc.common.entity.sast.QSastReportManagement;
import com.cqcdi.ngsoc.common.model.LoginHelper;
import com.cqcdi.ngsoc.common.model.LoginUser;
import com.cqcdi.ngsoc.sast.dto.ReportListRequest;
import com.cqcdi.ngsoc.sast.dto.GenerateReportRequest;
import com.cqcdi.ngsoc.sast.enums.DefectLevelEnum;
import com.cqcdi.ngsoc.sast.enums.TaskStatusEnum;
import com.cqcdi.ngsoc.sast.enums.VerificationResultEnum;
import com.cqcdi.ngsoc.sast.repo.pg.SastReportManagementRepo;
import com.cqcdi.ngsoc.sast.repo.pg.SastProjectManagementRepo;
import com.cqcdi.ngsoc.sast.repo.pg.SastTaskManagementRepo;
import com.cqcdi.ngsoc.sast.service.SastReportManagementService;
import com.cqcdi.ngsoc.sast.vo.*;
import com.cqcdi.ngsoc.common.entity.sast.QSastTaskManagement;
import com.cqcdi.ngsoc.common.entity.sast.QSastProjectManagement;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import com.cqcdi.ngsoc.sast.service.SastPdfReportService;


/**
 * SAST报告管理Service实现类
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@Slf4j
@Service
public class SastReportManagementServiceImpl implements SastReportManagementService {

    @Resource
    private SastReportManagementRepo reportRepository;

    @Resource
    private SastProjectManagementRepo projectRepository;

    @Resource
    private SastTaskManagementRepo taskRepository;

    @Resource
    protected JPAQueryFactory jpaQueryFactory;

    @Resource
    private SastPdfReportService pdfReportService;


    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public R<PageVo<ReportListItemVo>> getReportList(ReportListRequest request) {
        try {
            // 使用QueryDSL进行分页查询
            QSastReportManagement qReport = QSastReportManagement.sastReportManagement;
            JPAQuery<SastReportManagement> query = jpaQueryFactory
                    .select(qReport)
                    .from(qReport);

            // 构建查询条件
            BooleanExpression expression = qReport.delFlag.eq(0);

            // 关键词搜索（报告名称）
            if (StrUtil.isNotBlank(request.getKeyword())) {
                expression = expression.and(qReport.reportName.contains(request.getKeyword()));
            }

            // 应用查询条件并按报告生成时间倒序排列
            query.where(expression);
            query.orderBy(qReport.reportGenerationTime.desc());

            // 获取总数
            Long total = (long) query.fetch().size();

            // 分页查询
            List<SastReportManagement> reports = query
                    .offset((long) (request.getPage() - 1) * request.getSize())
                    .limit(request.getSize())
                    .fetch();

            // 转换为VO
            List<ReportListItemVo> reportVos = reports.stream()
                    .map(this::convertToReportListItemVo)
                    .collect(Collectors.toList());

            PageVo<ReportListItemVo> pageVo = new PageVo<>(reportVos, total, request.getSize(), request.getPage());
            return R.ok("查询成功", pageVo);

        } catch (Exception e) {
            log.error("获取报告列表失败: request={}, error={}", request, e.getMessage(), e);
            return R.fail("获取报告列表失败: " + e.getMessage());
        }
    }

    /**
     * 转换为报告列表项VO
     */
    private ReportListItemVo convertToReportListItemVo(SastReportManagement report) {
        ReportListItemVo vo = new ReportListItemVo();

        vo.setId(report.getId());
        vo.setReportName(report.getReportName());
        vo.setReportGenerationTime(report.getReportGenerationTime());

        // 查询项目名称
        Optional<SastProjectManagement> projectOpt = projectRepository.findById(report.getProjectId());
        if (projectOpt.isPresent()) {
            vo.setProjectName(projectOpt.get().getProjectName());
        } else {
            vo.setProjectName("未知项目");
        }

        // 查询任务名称
        Optional<SastTaskManagement> taskOpt = taskRepository.findById(report.getTaskId());
        if (taskOpt.isPresent()) {
            vo.setTaskName(taskOpt.get().getTaskName());
        } else {
            vo.setTaskName("未知任务");
        }

        // 统计缺陷数量
        vo.setDefectCount(report.getTotalDefectCount());

        // 统计漏洞分类
        VulnerabilityStatsVo vulnerabilityStats = new  VulnerabilityStatsVo();
        vulnerabilityStats.setHighRisk(report.getHighRiskCount());
        vulnerabilityStats.setMediumRisk(report.getMediumRiskCount());
        vulnerabilityStats.setLowRisk(report.getLowRiskCount());
        vo.setVulnerabilityStats(vulnerabilityStats);

        // 统计验证结果

        vo.setVerificationSuccess(report.getVerificationSuccessCount());
        vo.setVerificationFailure(report.getVerificationFailureCount());
        return vo;
    }


    /**
     * 辅助方法：安全获取Long值（处理null）
     */
    private Long getLongValue(Tuple tuple, String alias) {
        Object value = tuple.get(alias);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return 0L;
    }

    @Override
    public R<ProjectTaskOptionsVo> getProjectTaskOptions() {
        try {
            ProjectTaskOptionsVo options = new ProjectTaskOptionsVo();

            // 查询所有项目
            QSastProjectManagement qProject = QSastProjectManagement.sastProjectManagement;
            List<SastProjectManagement> projects = jpaQueryFactory
                    .select(qProject)
                    .from(qProject)
                    .where(qProject.delFlag.eq(0))
                    .orderBy(qProject.createdTime.desc())
                    .fetch();

            // 转换项目列表
            List<ProjectOptionVo> projectOptions = projects.stream()
                    .map(project -> {
                        ProjectOptionVo vo = new ProjectOptionVo();
                        vo.setProjectId(project.getId());
                        vo.setProjectName(project.getProjectName());
                        return vo;
                    })
                    .collect(Collectors.toList());
            options.setProjects(projectOptions);

            // 查询所有任务并按项目分组
            QSastTaskManagement qTask = QSastTaskManagement.sastTaskManagement;
            List<SastTaskManagement> tasks = jpaQueryFactory
                    .select(qTask)
                    .from(qTask)
                    .where(qTask.delFlag.eq(0))
                    .orderBy(qTask.createdTime.desc())
                    .fetch();

            // 按项目ID分组任务
            Map<String, List<TaskOptionVo>> tasksGroup = new HashMap<>();
            for (SastTaskManagement task : tasks) {
                String projectIdStr = task.getProjectId().toString();
                TaskOptionVo taskOption = new TaskOptionVo();
                taskOption.setTaskId(task.getTaskId());
                taskOption.setTaskName(task.getTaskName());

                tasksGroup.computeIfAbsent(projectIdStr, k -> new ArrayList<>()).add(taskOption);
            }
            options.setTasksGroup(tasksGroup);

            return R.ok("查询成功", options);

        } catch (Exception e) {
            log.error("获取项目任务选项失败: error={}", e.getMessage(), e);
            return R.fail("获取项目任务选项失败: " + e.getMessage());
        }
    }

    @Override
    public R<Void> deleteReport(Long reportId) {
        try {
            // 查询报告是否存在
            Optional<SastReportManagement> reportOpt = reportRepository.findById(reportId);
            if (reportOpt.isEmpty()) {
                return R.fail("报告不存在");
            }

            SastReportManagement report = reportOpt.get();

            // 检查是否已经删除
            if (report.getDelFlag() == 1) {
                return R.fail("报告不存在");
            }

            // 软删除：设置删除标志
            report.setDelFlag(1);
            reportRepository.save(report);

            return R.ok("报告删除成功");

        } catch (Exception e) {
            log.error("删除报告失败: reportId={}, error={}", reportId, e.getMessage(), e);
            return R.fail("删除报告失败: " + e.getMessage());
        }
    }

    @Override
    public R<Void> generateReport(GenerateReportRequest request) {
        try {
            Long projectId = Long.valueOf(request.getProjectId());
            Long taskId = Long.valueOf(request.getTaskId());

            // 1. 验证项目是否存在
            Optional<SastProjectManagement> projectOpt = projectRepository.findById(projectId);
            if (projectOpt.isEmpty() || projectOpt.get().getDelFlag() == 1) {
                return R.fail("项目不存在");
            }

            // 2. 验证任务是否存在且属于该项目
            Optional<SastTaskManagement> taskOpt = taskRepository.findById(taskId);
            if (taskOpt.isEmpty() || taskOpt.get().getDelFlag() == 1) {
                return R.fail("任务不存在或不属于该项目");
            }

            SastTaskManagement task = taskOpt.get();
            if (!task.getProjectId().equals(projectId)) {
                return R.fail("任务不存在或不属于该项目");
            }

            // 3. 检查任务状态是否完成
            // 如果任务状态不是完成状态，则不能生成报告
            if (!TaskStatusEnum.FIX_SUGGESTION.getStatus().equals(task.getTaskStatus())) {
                return R.fail("任务尚未完成，无法生成报告");
            }

            // 4. 检查报告名称是否重复
            QSastReportManagement qReport = QSastReportManagement.sastReportManagement;
            Long existingCount = jpaQueryFactory
                    .select(qReport.count())
                    .from(qReport)
                    .where(qReport.reportName.eq(request.getReportName())
                            .and(qReport.delFlag.eq(0)))
                    .fetchOne();

            if (existingCount > 0) {
                return R.fail("报告名称已存在");
            }

            // 5. 检查该任务是否已有正在生成的报告
            Long generatingCount = jpaQueryFactory
                    .select(qReport.count())
                    .from(qReport)
                    .where(qReport.taskId.eq(taskId)
                            .and(qReport.delFlag.eq(0)))
                    .fetchOne();

            if (generatingCount > 0) {
                // 查找已存在的报告ID
                Long existingReportId = jpaQueryFactory
                        .select(qReport.id)
                        .from(qReport)
                        .where(qReport.taskId.eq(taskId)
                                .and(qReport.delFlag.eq(0)))
                        .fetchFirst();
                return R.fail("该任务的报告正在生成中，请稍后再试");
            }

            // 6. 创建报告记录
            SastReportManagement report = createReportRecord(request, projectId, taskId);

            return R.ok("报告生成请求提交成功");

        } catch (NumberFormatException e) {
            log.error("生成报告失败，ID格式错误: request={}, error={}", request, e.getMessage(), e);
            return R.fail("项目ID或任务ID格式错误");
        } catch (Exception e) {
            log.error("生成报告失败: request={}, error={}", request, e.getMessage(), e);
            return R.fail("生成报告失败: " + e.getMessage());
        }
    }

    /**
     * 创建报告记录
     */
    private SastReportManagement createReportRecord(GenerateReportRequest request, Long projectId, Long taskId) {
        SastReportManagement report = new SastReportManagement();
        // 设置创建人信息
        LoginUser<?> user = LoginHelper.getLoginUser();
        if (user != null) {
            report.setCreatedId(Long.valueOf(user.getUserId()));
            report.setCreatedName(user.getNickname());
        }

        // 基本信息
        report.setReportName(request.getReportName());
        report.setProjectId(projectId);
        report.setTaskId(taskId);

        // 统计漏洞数据

        Tuple stats = reportRepository.countVulnerabilityStatsByTaskId(taskId);
        Integer highRiskCount = getLongValue(stats, DefectLevelEnum.HIGH.getAlias()).intValue();
        Integer MediumRiskCount =  getLongValue(stats, DefectLevelEnum.MEDIUM.getAlias()).intValue();
        Integer LowRiskCount =  getLongValue(stats, DefectLevelEnum.LOW.getAlias()).intValue();


        // 设置漏洞统计数据
        report.setHighRiskCount(highRiskCount);
        report.setMediumRiskCount(MediumRiskCount);
        report.setLowRiskCount(LowRiskCount);
        report.setTotalDefectCount(highRiskCount + MediumRiskCount + LowRiskCount);
        
        // 设置验证结果统计
        Tuple verificationResultsByTaskId = reportRepository.countVerificationResultsByTaskId(taskId);
        int success = getLongValue(verificationResultsByTaskId, VerificationResultEnum.SUCCESS.getAlias()).intValue();
        int fail = getLongValue(verificationResultsByTaskId, VerificationResultEnum.FAIL.getAlias()).intValue();
        report.setVerificationSuccessCount(success);
        report.setVerificationFailureCount(fail);

        // 保存报告
        report = reportRepository.save(report);

        // 生成HTML报告文件（使用FreeMarker模板）
        try {
            String htmlPath = pdfReportService.generateHtmlReportWithTemplate(report);
            log.info("HTML报告(FreeMarker)生成成功: reportId={}, htmlPath={}", report.getId(), htmlPath);
        } catch (Exception e) {
            log.error("HTML报告(FreeMarker)生成失败: reportId={}, error={}", report.getId(), e.getMessage(), e);
            // 不抛出异常，避免影响报告记录的保存
        }

        return report;
    }
}
