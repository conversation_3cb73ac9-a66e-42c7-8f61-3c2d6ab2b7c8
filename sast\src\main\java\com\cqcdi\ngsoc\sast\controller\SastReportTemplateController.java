/*
 * Copyright @ 2024 重庆市信息通信咨询设计院有限公司版权所有
 *
 * 项目名称: ngsoc-api
 * 文件名称: SastReportTemplateController.java
 *
 * 创建人: Weilq
 * 创建日期: 2025/7/31
 *
 * 版权描述:此软件未经重庆市信息通信咨询设计院有限公司许可，严禁发布、传播、使用
 * 公司地址:重庆市九龙坡区科园四路257号，400041.
 */
package com.cqcdi.ngsoc.sast.controller;

import com.cqcdi.ngsoc.common.entity.common.dto.R;
import com.cqcdi.ngsoc.common.entity.sast.SastReportManagement;
import com.cqcdi.ngsoc.sast.service.SastPdfReportService;
import com.cqcdi.ngsoc.sast.repo.pg.SastReportManagementRepo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.cqcdi.ngsoc.common.entity.sast.SastDefectInfo;

/**
 * SAST报告模板测试控制器
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@Slf4j
@RestController
@RequestMapping("/template")
@Tag(name = "通过模板报告生成测试", description = "SAST报告生成测试相关接口")
public class SastReportTemplateController {

    @Resource
    private SastPdfReportService pdfReportService;
    
    @Resource
    private SastReportManagementRepo reportRepository;
    
    @PersistenceContext
    private EntityManager entityManager;

    /**
     * 使用FreeMarker模板生成HTML报告
     *
     * @param reportId 报告ID
     * @return 报告文件路径
     */
    @GetMapping("/generate-html/{reportId}")
    @Operation(summary = "生成html报告", description = "生成一个包含示例数据的html报告")
    public R<String> generateHtmlReport(@PathVariable Long reportId) {
        try {
            Optional<SastReportManagement> reportOpt = reportRepository.findById(reportId);
            if (reportOpt.isEmpty()) {
                return R.fail("报告不存在");
            }
            
            SastReportManagement report = reportOpt.get();
            String htmlPath = pdfReportService.generateHtmlReportWithTemplate(report);
            
            return R.ok("HTML报告生成成功", htmlPath);
        } catch (Exception e) {
            log.error("生成HTML报告失败: reportId={}, error={}", reportId, e.getMessage(), e);
            return R.fail("生成HTML报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建示例报告并生成HTML报告
     *
     * @return 报告文件路径
     */
    @GetMapping("/generate-sample")
    public R<String> generateSampleReport() {
        try {
            SastReportManagement report = createSampleReport();
            String htmlPath = pdfReportService.generateHtmlReportWithTemplate(report);
            
            return R.ok("HTML报告生成成功", htmlPath);
        } catch (Exception e) {
            log.error("生成示例HTML报告失败: error={}", e.getMessage(), e);
            return R.fail("生成示例HTML报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 预览HTML报告
     *
     * @param reportId 报告ID
     * @return HTML报告内容
     */
    @GetMapping("/preview/{reportId}")
    public ResponseEntity<FileSystemResource> previewHtmlReport(@PathVariable Long reportId) {
        try {
            Optional<SastReportManagement> reportOpt = reportRepository.findById(reportId);
            if (reportOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            SastReportManagement report = reportOpt.get();
            String htmlPath = pdfReportService.generateHtmlReportWithTemplate(report);
            
            File file = new File(htmlPath);
            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"")
                    .body(new FileSystemResource(file));
        } catch (Exception e) {
            log.error("预览HTML报告失败: reportId={}, error={}", reportId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 创建示例报告
     */
    private SastReportManagement createSampleReport() {
        SastReportManagement report = new SastReportManagement();
        report.setId(1L);
        report.setReportName("示例报告");
        report.setProjectId(1L);
        report.setTaskId(1L);
        report.setHighRiskCount(231);
        report.setMediumRiskCount(356);
        report.setLowRiskCount(87);
        report.setTotalDefectCount(674);
        report.setVerificationSuccessCount(450);
        report.setVerificationFailureCount(224);
        report.setReportGenerationTime(LocalDateTime.now());
        // 根据实际SastReportManagement实体的属性设置，如果没有这些字段则删除
        // report.setCreatedTime(LocalDateTime.now());
        // report.setCreatedBy("system");
        // report.setUpdatedTime(LocalDateTime.now());
        // report.setUpdatedBy("system");
        report.setDelFlag(0);
        
        return report;
    }
    
    /**
     * 使用JPQL查询获取报告详情
     *
     * @param taskId 任务ID
     * @return 报告详情数据
     */
    @GetMapping("/report-details")
    @Operation(summary = "获取报告详情", description = "使用JPQL查询获取报告详情数据")
    public R<Map<String, Object>> getReportDetails(@RequestParam Long taskId) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 使用JPQL查询漏洞统计数据
            String statsJpql = "SELECT " +
                    "SUM(CASE WHEN d.defectLevel = '高危' THEN 1 ELSE 0 END) AS highRisk, " +
                    "SUM(CASE WHEN d.defectLevel = '中危' THEN 1 ELSE 0 END) AS mediumRisk, " +
                    "SUM(CASE WHEN d.defectLevel = '低危' THEN 1 ELSE 0 END) AS lowRisk " +
                    "FROM SastDefectInfo d WHERE d.taskId = :taskId AND d.delFlag = 0";
            
            Query statsQuery = entityManager.createQuery(statsJpql);
            statsQuery.setParameter("taskId", taskId);
            
            Object[] stats = (Object[]) statsQuery.getSingleResult();
            
            Map<String, Integer> vulnerabilityStats = new HashMap<>();
            if (stats != null && stats.length >= 3) {
                vulnerabilityStats.put("highRisk", stats[0] == null ? 0 : ((Number) stats[0]).intValue());
                vulnerabilityStats.put("mediumRisk", stats[1] == null ? 0 : ((Number) stats[1]).intValue());
                vulnerabilityStats.put("lowRisk", stats[2] == null ? 0 : ((Number) stats[2]).intValue());
                vulnerabilityStats.put("total", 
                        (stats[0] == null ? 0 : ((Number) stats[0]).intValue()) +
                        (stats[1] == null ? 0 : ((Number) stats[1]).intValue()) +
                        (stats[2] == null ? 0 : ((Number) stats[2]).intValue()));
            }
            result.put("stats", vulnerabilityStats);
            
            // 使用JPQL查询前5个高危漏洞
            String defectsJpql = "SELECT d FROM SastDefectInfo d " +
                    "WHERE d.taskId = :taskId AND d.defectLevel = '高危' AND d.delFlag = 0 " +
                    "ORDER BY d.id ASC";
            
            Query defectsQuery = entityManager.createQuery(defectsJpql);
            defectsQuery.setParameter("taskId", taskId);
            defectsQuery.setMaxResults(5);
            
            List<?> defects = defectsQuery.getResultList();
            List<Map<String, Object>> defectsList = new ArrayList<>();
            
            if (defects != null && !defects.isEmpty()) {
                for (Object obj : defects) {
                    if (obj instanceof SastDefectInfo) {
                        SastDefectInfo defect = (SastDefectInfo) obj;
                        Map<String, Object> defectMap = new HashMap<>();
                        defectMap.put("id", defect.getId());
                        defectMap.put("defectPath", defect.getDefectPath());
                        defectMap.put("vulnerabilityType", defect.getVulnerabilityType());
                        defectMap.put("defectLevel", defect.getDefectLevel());
                        defectMap.put("defectLine", defect.getDefectLine());
                        defectMap.put("defectFixSuggestion", defect.getDefectFixSuggestion());
                        defectsList.add(defectMap);
                    }
                }
            }
            result.put("highRiskDefects", defectsList);
            
            // 使用原生SQL查询漏洞类型分布
            String nativeSql = "SELECT vulnerability_type, COUNT(*) as count " +
                    "FROM app_sast_defect_info " +
                    "WHERE task_id = :taskId AND del_flag = 0 " +
                    "GROUP BY vulnerability_type " +
                    "ORDER BY count DESC";
            
            Query nativeQuery = entityManager.createNativeQuery(nativeSql);
            nativeQuery.setParameter("taskId", taskId);
            
            List<?> typeStats = nativeQuery.getResultList();
            List<Map<String, Object>> typeStatsList = new ArrayList<>();
            
            if (typeStats != null && !typeStats.isEmpty()) {
                for (Object obj : typeStats) {
                    if (obj instanceof Object[]) {
                        Object[] row = (Object[]) obj;
                        Map<String, Object> typeMap = new HashMap<>();
                        typeMap.put("type", row[0]);
                        typeMap.put("count", row[1]);
                        typeStatsList.add(typeMap);
                    }
                }
            }
            result.put("defectTypeStats", typeStatsList);
            
            return R.ok("查询成功", result);
        } catch (Exception e) {
            log.error("获取报告详情失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return R.fail("获取报告详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 使用JPQL查询获取漏洞详情
     *
     * @param defectId 漏洞ID
     * @return 漏洞详情数据
     */
    @GetMapping("/defect-detail")
    @Operation(summary = "获取漏洞详情", description = "使用JPQL查询获取单个漏洞的详细信息")
    public R<Map<String, Object>> getDefectDetail(@RequestParam Long defectId) {
        try {
            // 使用JPQL查询漏洞详情
            String jpql = "SELECT d FROM SastDefectInfo d WHERE d.id = :defectId AND d.delFlag = 0";
            
            Query query = entityManager.createQuery(jpql);
            query.setParameter("defectId", defectId);
            
            Object result = query.getSingleResult();
            
            if (result instanceof SastDefectInfo) {
                SastDefectInfo defect = (SastDefectInfo) result;
                Map<String, Object> defectMap = new HashMap<>();
                defectMap.put("id", defect.getId());
                defectMap.put("defectPath", defect.getDefectPath());
                defectMap.put("vulnerabilityType", defect.getVulnerabilityType());
                defectMap.put("defectLevel", defect.getDefectLevel());
                defectMap.put("defectLine", defect.getDefectLine());
                defectMap.put("defectFixSuggestion", defect.getDefectFixSuggestion());
                defectMap.put("defectChainInfo", defect.getDefectChainInfo());
                defectMap.put("defectLlmResults", defect.getDefectLlmResults());
                defectMap.put("defectVerificationResult", defect.getDefectVerificationResult());
                
                return R.ok("查询成功", defectMap);
            }
            
            return R.fail("漏洞不存在");
        } catch (Exception e) {
            log.error("获取漏洞详情失败: defectId={}, error={}", defectId, e.getMessage(), e);
            return R.fail("获取漏洞详情失败: " + e.getMessage());
        }
    }
} 