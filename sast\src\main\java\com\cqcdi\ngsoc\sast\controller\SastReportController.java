/*
 * Copyright @ 2024 重庆市信息通信咨询设计院有限公司版权所有
 *
 * 项目名称: ngsoc-api
 * 文件名称: SastReportController.java
 *
 * 创建人: Weilq
 * 创建日期: 2025/7/31
 *
 * 版权描述:此软件未经重庆市信息通信咨询设计院有限公司许可，严禁发布、传播、使用
 * 公司地址:重庆市九龙坡区科园四路257号，400041.
 */
package com.cqcdi.ngsoc.sast.controller;

import com.cqcdi.ngsoc.common.entity.common.dto.PageVo;
import com.cqcdi.ngsoc.common.entity.common.dto.R;
import com.cqcdi.ngsoc.common.entity.sast.SastReportManagement;
import com.cqcdi.ngsoc.sast.dto.ReportListRequest;
import com.cqcdi.ngsoc.sast.dto.GenerateReportRequest;
import com.cqcdi.ngsoc.sast.service.SastPdfReportService;
import com.cqcdi.ngsoc.sast.service.SastReportManagementService;
import com.cqcdi.ngsoc.sast.vo.ReportListItemVo;
import com.cqcdi.ngsoc.sast.vo.ProjectTaskOptionsVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;

/**
 * SAST报告管理控制器
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@Slf4j
@RestController
@RequestMapping("/reports")
@Tag(name = "报告管理", description = "SAST报告管理相关接口")
public class SastReportController {

    @Resource
    private SastPdfReportService pdfReportService;


    @Resource
    private SastReportManagementService reportManagementService;

    /**
     * 获取报告列表（分页）
     */
    @GetMapping("/list")
    @Operation(summary = "获取报告列表（分页）", description = "分页查询报告信息列表，支持搜索筛选")
    public R<PageVo<ReportListItemVo>> getReportList(@Valid ReportListRequest request) {
        log.info("获取报告列表: {}", request);
        return reportManagementService.getReportList(request);
    }

    /**
     * 获取项目和任务选项
     */
    @GetMapping("/projects_tasks/options")
    @Operation(summary = "获取项目和任务选项", description = "获取项目列表和任务列表")
    public R<ProjectTaskOptionsVo> getProjectTaskOptions() {
        log.info("获取项目和任务选项");
        return reportManagementService.getProjectTaskOptions();
    }

    /**
     * 删除报告
     */
    @DeleteMapping("/{reportId}")
    @Operation(summary = "删除报告", description = "删除指定的报告（软删除）")
    @Parameter(name = "reportId", description = "报告ID", required = true)
    public R<Void> deleteReport(@PathVariable Long reportId) {
        log.info("删除报告: reportId={}", reportId);
        return reportManagementService.deleteReport(reportId);
    }

    /**
     * 生成报告
     */
    @PostMapping("/generate")
    @Operation(summary = "生成报告", description = "根据选择的项目和任务生成分析报告")
    public R<Void> generateReport(@Valid @RequestBody GenerateReportRequest request) {
        log.info("生成报告: {}", request);
        return reportManagementService.generateReport(request);
    }
}
