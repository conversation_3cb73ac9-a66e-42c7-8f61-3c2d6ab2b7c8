/*
 * Copyright @ 2024 重庆市信息通信咨询设计院有限公司版权所有
 *
 * 项目名称: ngsoc-api
 * 文件名称: FreemarkerConfig.java
 *
 * 创建人: Weilq
 * 创建日期: 2025/7/31
 *
 * 版权描述:此软件未经重庆市信息通信咨询设计院有限公司许可，严禁发布、传播、使用
 * 公司地址:重庆市九龙坡区科园四路257号，400041.
 */
package com.cqcdi.ngsoc.sast.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

/**
 * FreeMarker配置类
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@Configuration
public class FreemarkerConfig {

    /**
     * 配置FreeMarker
     */
    @Bean
    public FreeMarkerConfigurer freeMarkerConfigurer() {
        FreeMarkerConfigurer configurer = new FreeMarkerConfigurer();
        configurer.setTemplateLoaderPath("classpath:/templates/");
        configurer.setDefaultEncoding("UTF-8");
        return configurer;
    }
} 