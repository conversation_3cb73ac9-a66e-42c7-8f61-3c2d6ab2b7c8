---
title: 下一代智能安全防控平台
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 下一代智能安全防控平台

Base URLs:

# Authentication

# SAST/项目管理

## POST 新增项目

POST /sast/project-management/project

> Body 请求参数

```json
{
  "project_name": "test",
  "project_description": "这是一个测试项目"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» project_name|body|string| 是 |none|
|» project_description|body|string| 是 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

<a id="opIdgetProjectTaskList"></a>

## GET 获取项目任务列表

GET /sast/project-management/{projectId}/tasks

分页获取指定项目下的任务列表，包含任务基本信息、文件上传状态、分析状态、漏洞统计等信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|integer(int64)| 是 |项目ID|
|keyword|query|string| 否 |搜索关键词（支持任务名称、创建人模糊搜索）|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "summary": {
      "totalTasks": 265,
      "highRisk": 140,
      "highRiskPercent": 30,
      "mediumRisk": 109,
      "mediumRiskPercent": 30,
      "lowRisk": 87,
      "lowRiskPercent": 40
    },
    "taskList": [
      {
        "taskId": "TASK-20250723-001",
        "taskName": "测试智能化系统",
        "fileUploadStatus": {
          "sourceFileProgress": 100,
          "configFileProgress": 100
        },
        "analysisStatus": "AI分析完成",
        "vulnerabilityStats": {
          "highRisk": 12,
          "mediumRisk": 8,
          "lowRisk": 5
        },
        "updateTime": "2024-03-12T12:00:00"
      }
    ]
  }
}
```

> 400 Response

```json
{
  "code": 400,
  "msg": "参数错误",
  "data": null
}
```

> 500 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取项目任务列表|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||响应状态码|
|» msg|string|false|none||响应消息|
|» data|object|false|none||none|
|»» summary|[TaskSummary](#schematasksummary)|false|none||任务统计汇总|
|»»» totalTasks|integer|false|none||测试总数|
|»»» highRisk|integer|false|none||高危漏洞总数|
|»»» highRiskPercent|integer|false|none||高危漏洞占比(%)|
|»»» mediumRisk|integer|false|none||中危漏洞总数|
|»»» mediumRiskPercent|integer|false|none||中危漏洞占比(%)|
|»»» lowRisk|integer|false|none||低危漏洞总数|
|»»» lowRiskPercent|integer|false|none||低危漏洞占比(%)|
|»» taskList|[object]|false|none||任务列表数据|
|»»» taskId|string|true|none||任务ID|
|»»» taskName|string|true|none||任务名称|
|»»» fileUploadStatus|[FileUploadStatus](#schemafileuploadstatus)|false|none||文件上传状态|
|»»»» sourceFileProgress|integer|false|none||源文件上传进度(%)|
|»»»» configFileProgress|integer|false|none||环境配置文件上传进度(%)|
|»»» analysisStatus|string|false|none||分析状态|
|»»» vulnerabilityStats|[AnalysisResults](#schemaanalysisresults)|false|none||漏洞数量|
|»»»» highRisk|integer|false|none||高危漏洞数量|
|»»»» mediumRisk|integer|false|none||中危漏洞数量|
|»»»» lowRisk|integer|false|none||低危漏洞数量|
|»»» updateTime|string(date-time)|true|none||更新时间|

#### 枚举值

|属性|值|
|---|---|
|analysisStatus|排队|
|analysisStatus|传播器查找|
|analysisStatus|链路分析|
|analysisStatus|研判|
|analysisStatus|POC验证|
|analysisStatus|修复建议生成|
|analysisStatus|AI分析完成|
|analysisStatus|分析失败|

状态码 **400**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdcreateTask"></a>

## POST 创建新任务

POST /sast/project-management/{projectId}/tasks

创建新的SAST任务，提交任务基本信息

> Body 请求参数

```json
{
  "taskName": "struts检测",
  "taskLanguage": "Java",
  "analysisMethod": "离线分析",
  "buildMethod": "Java-maven",
  "compiledFileId": "34454646",
  "sourceCodeFileId": "3445565756"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|projectId|path|integer(int64)| 是 |项目ID|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» taskName|body|string| 是 |none|
|» taskLanguage|body|string| 是 |none|
|» analysisMethod|body|string| 是 |none|
|» buildMethod|body|string| 是 |none|
|» compiledFileId|body|string| 否 |编译文件|
|» sourceCodeFileId|body|string| 是 |源码文件|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "taskId": 123456,
    "taskName": "struts检测",
    "taskStatus": "排队"
  }
}
```

> 400 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|任务创建成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object|false|none||none|
|»» taskId|integer(int64)|false|none||创建的任务ID|
|»» taskName|string|false|none||任务名称|
|»» taskStatus|string|false|none||任务状态|

状态码 **400**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIduploadTaskFile"></a>

## POST 上传任务文件

POST /sast/files/upload

为指定任务上传源代码文件或预编译文件

> Body 请求参数

```yaml
fileType: 源码文件
file: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» fileType|body|string| 是 |文件类型|
|» file|body|string(binary)| 是 |上传的文件|

#### 枚举值

|属性|值|
|---|---|
|» fileType|源码文件|
|» fileType|预编译压缩包|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "fileId": "354545455",
    "fileName": "struts源码编译文件.xlsx"
  }
}
```

> 400 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|文件上传成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» fileId|string|true|none||none|
|»» fileName|string|true|none||none|

状态码 **400**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdgetProjectList"></a>

## GET 获取项目管理列表

GET /sast/project-management/project/list

分页获取项目管理列表，包含项目基本信息、任务统计、产品阶段等信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |页码，从1开始|
|size|query|integer| 否 |每页大小|
|keyword|query|string| 否 |搜索关键词（支持项目名称、任务名称、创建人模糊搜索）|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "操作成功!",
  "data": {
    "records": [
      {
        "id": 1,
        "projectName": "测试智能化系统安全分析项目",
        "projectDescription": "通过智能化技术对系统进行全方位的安全分析和漏洞检测",
        "taskStats": "5/8",
        "vulnerabilityStats": {
          "highRisk": 100,
          "mediumRisk": 467,
          "lowRisk": 379
        },
        "createdTime": "2025-03-12T12:23:11"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "pages": 0
  }
}
```

> 400 Response

```json
{
  "code": 400,
  "msg": "参数错误",
  "data": null
}
```

> 500 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取项目列表|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||响应状态码|
|» msg|string|true|none||响应消息|
|» data|object|true|none||none|
|»» records|[object]|true|none||none|
|»»» id|integer|false|none||none|
|»»» projectName|string|false|none||项目名|
|»»» projectDescription|string|false|none||项目描述|
|»»» taskStats|string|false|none||任务完成，5\8|
|»»» vulnerabilityStats|object|false|none||漏洞数量|
|»»»» highRisk|integer|true|none||none|
|»»»» mediumRisk|integer|true|none||none|
|»»»» lowRisk|integer|true|none||none|
|»»» createdTime|string|false|none||none|
|»» total|integer|true|none||none|
|»» size|integer|true|none||none|
|»» current|integer|true|none||none|
|»» pages|integer|true|none||none|
|» result|object|false|none||none|
|»» data|[object]|false|none||项目列表数据|
|»»» id|integer(int64)|true|none||项目ID|
|»»» projectName|string|true|none||项目名称|
|»»» projectDescription|string|false|none||项目描述|
|»»» taskStats|string|false|none||任务统计（格式：已完成/总数）|
|»»» vulnerabilityStats|[VulnerabilityStats](#schemavulnerabilitystats)|false|none||漏洞统计|
|»»»» highRisk|integer|false|none||高危漏洞数量|
|»»»» mediumRisk|integer|false|none||中危漏洞数量|
|»»»» lowRisk|integer|false|none||低危漏洞数量|
|»»» createdTime|string(date-time)|true|none||创建时间|
|»» total|integer|false|none||总记录数|
|»» num|integer|false|none||当前页记录数|

状态码 **400**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

# SAST/缺陷管理

<a id="opIdgetTaskDefectTree"></a>

## GET 获取任务缺陷树形结构

GET /sast/defects/tree

根据任务ID获取缺陷的分类树形结构，按漏洞类型分组显示，支持搜索筛选

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|keyword|query|string| 否 |搜索关键词（缺陷路径名称）|
|taskId|query|string| 是 |任务id|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "totalCount": 17,
    "vulnerabilityTypes": [
      {
        "typeName": "SQL注入",
        "typeCount": 5,
        "defects": [
          {
            "defectId": "1",
            "defectPath": "VulnCore/JDBCAttack/src/main/java/hmy/BMIAttack.java"
          },
          {
            "defectId": "3",
            "defectPath": "ulnCore/Inject/SQL/src/main/java/com/spp/mysql/MysqlInject.java"
          },
          {
            "defectId": "6",
            "defectPath": "VulnCore/JDBCAttack/src/main/java/hzd/database/HZAttack.java"
          }
        ]
      },
      {
        "typeName": "RCE",
        "typeCount": 12,
        "defects": [
          {
            "defectId": "2",
            "defectPath": "VulnCore/XXE/src/main/java/org/example/XXEAttack.java"
          },
          {
            "defectId": "4",
            "defectPath": "VulnCore/XXE/src/main/java/org/example/XXEAttack.java"
          }
        ]
      }
    ]
  }
}
```

> 404 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取缺陷树形结构|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|任务不存在|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» totalCount|integer|true|none||none|
|»» vulnerabilityTypes|[object]|true|none||none|
|»»» typeName|string|true|none||none|
|»»» typeCount|integer|true|none||none|
|»»» defects|[object]|true|none||none|
|»»»» defectId|string|true|none||none|
|»»»» defectPath|string|true|none||none|

状态码 **404**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdgetDefectStatistics"></a>

## GET 获取缺陷统计信息

GET /sast/defects/statistics

获取缺陷总数和各危险等级的统计数据

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|taskId|query|string| 否 |任务ID（可选，不传则统计所有任务）|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "totalCount": 265,
    "verificationSuccess": 343,
    "verificationFail": 34,
    "highRiskCount": 140,
    "highRiskPercentage": 30,
    "mediumRiskCount": 109,
    "mediumRiskPercentage": 30,
    "lowRiskCount": 87,
    "lowRiskPercentage": 40
  }
}
```

> 500 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取缺陷统计信息|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object|false|none||none|
|»» totalCount|integer|true|none||漏洞总数|
|»» verificationSuccess|string|true|none||验证成功|
|»» verificationFail|string|true|none||验证失败|
|»» highRiskCount|integer|true|none||高危漏洞数量|
|»» highRiskPercentage|integer|true|none||高危漏洞占比（百分比）|
|»» mediumRiskCount|integer|true|none||中危漏洞数量|
|»» mediumRiskPercentage|integer|true|none||中危漏洞占比（百分比）|
|»» lowRiskCount|integer|true|none||低危漏洞数量|
|»» lowRiskPercentage|integer|true|none||低危漏洞占比（百分比）|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdgetDefectList"></a>

## GET 获取缺陷列表（分页）

GET /sast/defects/list

分页查询缺陷信息列表，支持搜索和筛选，可用于项目管理项目缺陷页，也可用于缺陷管理缺陷页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |当前页码|
|size|query|integer| 否 |每页记录数|
|keyword|query|string| 否 |搜索关键词（缺陷路径）|
|projectId|query|string| 否 |项目ID筛选|
|taskId|query|string| 否 |任务ID筛选|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": "343434",
        "vulnerabilityType": "SQL注入",
        "projectName": "靶场测试",
        "taskName": "测试智能检测试",
        "defectPath": "VulnCore/JDBCAttack/src/main/java/hmy/BMIAttack.java",
        "defectLine": 22,
        "defectLevel": "高危",
        "verificationResult": "验证成功",
        "defectFixSuggestion": "sql防范需要注意参数化查询",
        "createdTime": "2025-03-12 12:23:11",
        "taskId": "123456",
        "projectId": "1"
      }
    ],
    "total": 265,
    "size": 10,
    "current": 1,
    "pages": 27
  }
}
```

> 400 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取缺陷列表|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object|false|none||none|
|»» records|[object]|false|none||none|
|»»» id|string|false|none||缺陷ID|
|»»» vulnerabilityType|string|false|none||漏洞类型|
|»»» projectName|string|true|none||项目名称|
|»»» taskName|string|false|none||所属任务名称|
|»»» defectPath|string|false|none||缺陷对象路径|
|»»» defectLine|integer|false|none||缺陷行号|
|»»» defectLevel|string|false|none||危险程度|
|»»» verificationResult|string|false|none||验证结果|
|»»» defectFixSuggestion|string|false|none||修复建议|
|»»» createdTime|string|false|none||创建时间|
|»»» taskId|string|false|none||所属任务ID|
|»»» projectId|string|false|none||所属项目ID|
|»» total|integer|false|none||总记录数|
|»» size|integer|false|none||每页记录数|
|»» current|integer|false|none||当前页码|
|»» pages|integer|false|none||总页数|

#### 枚举值

|属性|值|
|---|---|
|defectLevel|高危|
|defectLevel|中危|
|defectLevel|低危|
|verificationResult|验证成功|
|verificationResult|验证失败|
|verificationResult|未验证|

状态码 **400**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdgetDefectDetail"></a>

## GET 获取缺陷详情

GET /sast/defects/{defectId}

获取指定缺陷的详细信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|defectId|path|string| 是 |缺陷ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": "1",
    "sequenceNumber": "01",
    "vulnerabilityType": "SQL注入",
    "projectName": "测试智能检测试",
    "taskName": "智能检测试任务",
    "defectPath": "VulnCore/JDBCAttack/src/main/java/hmy/BMIAttack.java",
    "defectLine": 22,
    "defectLevel": "高危",
    "verificationResult": "验证成功",
    "createdTime": "2025-03-12 12:23:11",
    "taskId": "123456",
    "projectId": "1",
    "defectFixSuggestion": "使用参数化查询或预编译语句来防止SQL注入攻击",
    "defectChainInfo": {},
    "defectLlmResults": []
  }
}
```

> 404 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取缺陷详情|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|缺陷不存在|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|
|»» vulnerabilityType|string|true|none||none|
|»» projectName|string|true|none||none|
|»» taskName|string|true|none||none|
|»» defectPath|string|true|none||none|
|»» defectLine|integer|true|none||none|
|»» defectLevel|string|true|none||none|
|»» verificationResult|string|true|none||none|
|»» createdTime|string|true|none||none|
|»» taskId|string|true|none||none|
|»» projectId|string|true|none||none|
|»» defectFixSuggestion|string|true|none||none|
|»» defectChainInfo|object|true|none||具体的漏洞代码和步骤信息|
|»» defectLlmResults|[object]|true|none||none|

状态码 **404**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

# SAST/模型配置

<a id="opIdgetModelList"></a>

## GET 获取模型配置列表（分页）

GET /sast/model-configs

分页查询模型配置信息列表，支持搜索和筛选

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|keyword|query|string| 否 |搜索关键词（模型名称）|
|supplier|query|string| 否 |供应商筛选|
|testStatus|query|string| 否 |测试状态筛选|
|Authorization|header|string| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|testStatus|正常|
|testStatus|异常|
|testStatus|未测试|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": "232332324",
        "modelName": "GPT-4 通用模型",
        "supplier": "OpenAI",
        "modelIdentifier": "gpt-4-turbo",
        "testStatus": "正常",
        "modifyTime": "2024-03-12 12:00:00"
      },
      {
        "id": "2334354545",
        "modelName": "文心一言安全版",
        "supplier": "百度",
        "modelIdentifier": "ERNIE-Bot-Security",
        "testStatus": "正常",
        "modifyTime": "2024-03-12 12:00:00"
      },
      {
        "id": "345456",
        "modelName": "Claude 3",
        "supplier": "Anthropic",
        "modelIdentifier": "claude-3-opus",
        "testStatus": "正常",
        "modifyTime": "2024-03-12 12:00:00"
      }
    ],
    "total": 3,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

> 400 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取模型配置列表|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object|false|none||none|
|»» records|[object]|false|none||none|
|»»» id|string|false|none||模型ID|
|»»» modelName|string|false|none||模型名称|
|»»» supplier|string|false|none||组织商/供应商|
|»»» modelIdentifier|string|false|none||模型标识|
|»»» testStatus|string|false|none||测试状态|
|»»» modifyTime|string|false|none||更新时间|
|»» total|integer|false|none||总记录数|
|»» size|integer|false|none||每页记录数|
|»» current|integer|false|none||当前页码|
|»» pages|integer|false|none||总页数|

#### 枚举值

|属性|值|
|---|---|
|testStatus|正常|
|testStatus|异常|
|testStatus|未测试|

状态码 **400**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdcreateModel"></a>

## POST 新增模型配置

POST /sast/model-configs

创建新的模型配置

> Body 请求参数

```json
{
  "modelName": "GPT-4 通用模型",
  "supplier": "OpenAI",
  "modelIdentifier": "gpt-4-turbo",
  "apiType": "REST",
  "baseUrl": "https://api.openai.com/v1",
  "apiKey": "sk-xxxxxxxxxxxxxxxx",
  "maxTokens": 4000,
  "temperature": 0.7,
  "timeoutSetting": 30,
  "extraHeaders": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» modelName|body|string| 是 |模型名称|
|» supplier|body|string| 是 |供应商|
|» modelIdentifier|body|string| 是 |模型标识|
|» apiType|body|string| 是 |API类型|
|» baseUrl|body|string| 是 |基础URL|
|» apiKey|body|string| 是 |API密钥|
|» maxTokens|body|integer| 否 |最大token数|
|» temperature|body|number(float)| 否 |输出温度|
|» timeoutSetting|body|integer| 否 |超时设置（秒）|
|» extraHeaders|body|object| 否 |额外的请求头|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": null
}
```

> 400 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|模型配置创建成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object|false|none||none|

状态码 **400**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdgetModelDetail"></a>

## GET 获取模型配置详情

GET /sast/model-configs/{modelId}

获取指定模型的详细配置信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|modelId|path|integer(int64)| 是 |模型ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": 1,
    "modelName": "GPT-4 通用模型",
    "supplier": "OpenAI",
    "modelIdentifier": "gpt-4-turbo",
    "testStatus": "正常",
    "modifyTime": "2024-03-12 12:00:00",
    "apiType": "REST",
    "baseUrl": "https://api.openai.com/v1",
    "apiKey": "sk-**********************",
    "maxTokens": 4000,
    "temperature": 0.7,
    "timeoutSetting": 30,
    "extraHeaders": {},
    "createdName": "admin",
    "createdTime": "2024-03-12 10:00:00"
  }
}
```

> 404 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取模型配置详情|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|模型配置不存在|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|
|»» modelName|string|true|none||none|
|»» supplier|string|true|none||none|
|»» modelIdentifier|string|true|none||none|
|»» testStatus|string|true|none||none|
|»» modifyTime|string|true|none||none|
|»» apiType|string|true|none||none|
|»» baseUrl|string|true|none||none|
|»» apiKey|string|true|none||none|
|»» maxTokens|integer|true|none||none|
|»» temperature|number|true|none||none|
|»» timeoutSetting|integer|true|none||none|
|»» extraHeaders|object|true|none||none|
|»» createdName|string|true|none||none|
|»» createdTime|string|true|none||none|

状态码 **404**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdupdateModel"></a>

## PUT 更新模型配置

PUT /sast/model-configs/{modelId}

更新指定模型的配置信息

> Body 请求参数

```json
{
  "modelName": "GPT-4 通用模型",
  "supplier": "OpenAI",
  "modelIdentifier": "gpt-4-turbo",
  "apiType": "REST",
  "baseUrl": "https://api.openai.com/v1",
  "apiKey": "sk-xxxxxxxxxxxxxxxx",
  "maxTokens": 4000,
  "temperature": 0.7,
  "timeoutSetting": 30,
  "extraHeaders": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|modelId|path|integer(int64)| 是 |模型ID|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» modelName|body|string| 否 |模型名称|
|» supplier|body|string| 否 |供应商|
|» modelIdentifier|body|string| 否 |模型标识|
|» apiType|body|string| 否 |API类型|
|» baseUrl|body|string| 否 |基础URL|
|» apiKey|body|string| 否 |API密钥|
|» maxTokens|body|integer| 否 |最大token数|
|» temperature|body|number(float)| 否 |输出温度|
|» timeoutSetting|body|integer| 否 |超时设置（秒）|
|» extraHeaders|body|object| 否 |额外的请求头|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|模型配置更新成功|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|模型配置不存在|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object¦null|false|none||none|

状态码 **404**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIddeleteModel"></a>

## DELETE 删除模型配置

DELETE /sast/model-configs/{modelId}

删除指定的模型配置（软删除）

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|modelId|path|integer(int64)| 是 |模型ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|模型配置删除成功|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|模型配置不存在|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object¦null|false|none||none|

状态码 **404**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

# SAST/模型配置/模型测试

<a id="opIdgetAvailableModels"></a>

## GET 获取可用模型列表

GET /sast/model-configs/available

获取所有可用于测试的模型列表（用于模型测试弹窗的下拉选择）

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "modelId": 1,
      "modelName": "GPT-4 通用模型",
      "supplier": "OpenAI",
      "modelIdentifier": "gpt-4-turbo"
    },
    {
      "modelId": 2,
      "modelName": "文心一言安全版",
      "supplier": "百度",
      "modelIdentifier": "ERNIE-Bot-Security"
    }
  ]
}
```

> 500 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取可用模型列表|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|[object]|false|none||none|
|»» modelId|integer(int64)|false|none||模型ID|
|»» modelName|string|false|none||模型名称|
|»» supplier|string|false|none||供应商|
|»» modelIdentifier|string|false|none||模型标识|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdexecuteModelTest"></a>

## POST 执行模型测试

POST /sast/model-configs/test

对指定模型进行功能测试，包含自定义测试内容

> Body 请求参数

```json
{
  "modelId": 1,
  "testContent": "请输入测试内容，例如一段代码，用户输入或系统目标，用于模型检测..."
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» modelId|body|integer(int64)| 是 |选择的模型ID|
|» testContent|body|string| 是 |测试输入内容|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "testId": "test_20240312_001",
    "modelId": 1,
    "modelName": "GPT-4 通用模型",
    "testContent": "请输入测试内容...",
    "testOutput": "模型输出将显示在这里",
    "responseTime": 2.5,
    "tokenUsage": 150,
    "testStatus": "成功",
    "testTime": "2024-03-12T12:00:00"
  }
}
```

> 400 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|模型测试执行成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|模型不存在|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object|false|none||none|
|»» testId|string|false|none||测试任务ID|
|»» modelId|integer(int64)|false|none||模型ID|
|»» modelName|string|false|none||模型名称|
|»» testContent|string|false|none||测试输入内容|
|»» testOutput|string|true|none||模型输出结果|
|»» responseTime|number(float)|true|none||响应时间（秒）|
|»» tokenUsage|integer|true|none||Token消耗|
|»» testStatus|string|true|none||测试状态|
|»» testTime|string(date-time)|false|none||测试时间|
|»» errorMessage|string¦null|false|none||错误信息（如果测试失败）|

#### 枚举值

|属性|值|
|---|---|
|testStatus|成功|
|testStatus|失败|
|testStatus|进行中|

状态码 **400**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **404**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

# SAST/模型配置/权重配置

<a id="opIdgetModelWeights"></a>

## GET 获取模型权重配置

GET /api/v1/sast/model-config/models/weights

获取当前所有模型的权重配置信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "models": [
      {
        "modelId": 1,
        "modelName": "GPT-4 漏洞检测",
        "currentWeight": 30
      },
      {
        "modelId": 2,
        "modelName": "文心一言安全版",
        "currentWeight": 20
      },
      {
        "modelId": 3,
        "modelName": "Claude-3",
        "currentWeight": 50
      }
    ],
    "totalWeight": 100,
    "isValid": true
  }
}
```

> 500 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取模型权重配置|Inline|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object|false|none||none|
|»» models|[object]|false|none||模型权重列表|
|»»» modelId|integer(int64)|true|none||模型ID|
|»»» modelName|string|true|none||模型名称|
|»»» currentWeight|number(float)|true|none||当前权重百分比|
|»» totalWeight|number(float)|false|none||当前总权重|
|»» isValid|boolean|false|none||权重配置是否有效（总和是否为100%）|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdupdateModelWeights"></a>

## PUT 更新模型权重配置

PUT /api/v1/sast/model-config/models/weights

批量更新模型权重配置，支持平均分配功能

> Body 请求参数

```json
{
  "weights": [
    {
      "modelId": 1,
      "weight": 30
    },
    {
      "modelId": 2,
      "weight": 20
    },
    {
      "modelId": 3,
      "weight": 50
    }
  ],
  "autoBalance": false
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» weights|body|[object]| 是 |模型权重配置列表|
|»» modelId|body|integer(int64)| 是 |模型ID|
|»» weight|body|number(float)| 是 |权重百分比 (0-100)|
|» autoBalance|body|boolean| 否 |是否自动平均分配权重|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "updatedModels": [
      {
        "modelId": 1,
        "modelName": "GPT-4 漏洞检测",
        "currentWeight": 30
      }
    ],
    "totalWeight": 100
  }
}
```

> 400 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|权重配置更新成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误（如权重总和不为100%）|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object|false|none||none|
|»» updatedModels|[object]|false|none||更新后的模型权重|
|»»» modelId|integer(int64)|true|none||模型ID|
|»»» modelName|string|true|none||模型名称|
|»»» currentWeight|number(float)|true|none||当前权重百分比|
|»» totalWeight|number(float)|false|none||更新后的总权重|

状态码 **400**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

# SAST/报告管理

<a id="opIdgetTaskOptionsByProject"></a>

## GET 获取项目和任务选项

GET /sast/reports/projects_tasks/options

获取项目列表和任务列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "projects": [
      {
        "projectId": 1001,
        "projectName": "金融核心系统"
      },
      {
        "projectId": 1002,
        "projectName": "电商平台重构"
      }
    ],
    "tasksGroup": {
      "1001": [
        {
          "taskId": 2001,
          "taskName": "SQL注入扫描"
        },
        {
          "taskId": 2002,
          "taskName": "XSS验证"
        }
      ],
      "1002": [
        {
          "taskId": 3001,
          "taskName": "组件漏洞分析"
        }
      ]
    }
  }
}
```

> 404 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取任务选项列表|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|项目不存在|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» projects|[object]|true|none||none|
|»»» projectId|string|true|none||none|
|»»» projectName|string|true|none||none|
|»» tasksGroup|object|true|none||none|
|»»» 1001|[object]|true|none|项目id|none|
|»»»» taskId|integer|true|none||none|
|»»»» taskName|string|true|none||none|
|»»» 1002|[object]|true|none||none|
|»»»» taskId|integer|false|none||none|
|»»»» taskName|string|false|none||none|

状态码 **404**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdgenerateReport"></a>

## POST 生成报告

POST /sast/reports/generate

根据选择的项目和任务生成分析报告

> Body 请求参数

```json
{
  "reportName": "漏洞测试分析报告",
  "projectId": "1",
  "taskId": "123456"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» reportName|body|string| 是 |none|
|» projectId|body|string| 是 |none|
|» taskId|body|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": null
}
```

> 请求参数错误

```json
{
  "code": 400,
  "msg": "参数验证失败",
  "data": {
    "errors": [
      {
        "field": "reportName",
        "message": "报告名称不能为空"
      },
      {
        "field": "projectId",
        "message": "项目ID必须为正整数"
      }
    ]
  }
}
```

```json
{
  "code": 400,
  "msg": "报告名称已存在",
  "data": null
}
```

> 项目或任务不存在

```json
{
  "code": 404,
  "msg": "项目不存在",
  "data": null
}
```

```json
{
  "code": 404,
  "msg": "任务不存在或不属于该项目",
  "data": null
}
```

> 业务冲突

```json
{
  "code": 409,
  "msg": "任务尚未完成，无法生成报告",
  "data": {
    "taskStatus": "POC验证"
  }
}
```

```json
{
  "code": 409,
  "msg": "该任务的报告正在生成中，请稍后再试",
  "data": {
    "existingReportId": 10
  }
}
```

> 500 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|报告生成请求提交成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|项目或任务不存在|[ErrorResponse](#schemaerrorresponse)|
|409|[Conflict](https://tools.ietf.org/html/rfc7231#section-6.5.8)|业务冲突|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|null|true|none||none|

状态码 **400**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **404**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **409**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdgetReportList"></a>

## GET 获取报告列表（分页）

GET /sast/reports/list

分页查询报告信息列表，支持搜索和筛选

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |当前页码|
|size|query|integer| 否 |每页记录数|
|keyword|query|string| 否 |搜索关键词（项目名称，任务名称）|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": "1",
        "reportName": "漏洞测试分析报告",
        "projectName": "cms系统项目",
        "taskName": "漏洞检测测试",
        "defectCount": 134,
        "vulnerabilityStats": {
          "highRisk": 100,
          "mediumRisk": 30,
          "lowRisk": 4
        },
        "verificationSuccess": 130,
        "verificationFailure": 4,
        "reportGenerationTime": "2024-03-12 12:00:00"
      },
      {
        "id": "2",
        "reportName": "漏洞测试分析报告",
        "projectName": "cms系统项目",
        "taskName": "漏洞检测测试",
        "defectCount": 134,
        "vulnerabilityStats": {
          "highRisk": 100,
          "mediumRisk": 30,
          "lowRisk": 4
        },
        "verificationSuccess": 130,
        "verificationFailure": 4,
        "reportGenerationTime": "2024-03-12 12:00:00"
      }
    ],
    "total": 10,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

> 400 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取报告列表|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object|false|none||none|
|»» records|[object]|false|none||none|
|»»» id|string|true|none||报告ID|
|»»» reportName|string|true|none||报告名称|
|»»» projectName|string|true|none||项目名称|
|»»» taskName|string|true|none||任务名称|
|»»» defectCount|integer|true|none||缺陷数量|
|»»» vulnerabilityStats|object|true|none||危险分类|
|»»»» highRisk|integer|false|none||高危数量|
|»»»» mediumRisk|integer|false|none||中危数量|
|»»»» lowRisk|integer|false|none||低危数量|
|»»» verificationSuccess|integer|true|none||验证成功数量|
|»»» verificationFailure|integer|true|none||验证失败数量|
|»»» reportGenerationTime|string|true|none||报告生成时间|
|»» total|integer|false|none||总记录数|
|»» size|integer|false|none||每页记录数|
|»» current|integer|false|none||当前页码|
|»» pages|integer|false|none||总页数|

状态码 **400**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIdgetReportDetail"></a>

## GET 查看报告预览（pdf）

GET /sast/reports/{reportId}

获取指定报告的详细信息和内容

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|reportId|path|integer(int64)| 是 |报告ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{}
```

> 404 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取报告详情|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|报告不存在|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **404**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIddeleteReport"></a>

## DELETE 删除报告

DELETE /sast/reports/{reportId}

删除指定的报告（软删除）

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|reportId|path|integer(int64)| 是 |报告ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|报告删除成功|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|报告不存在|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object¦null|false|none||none|

状态码 **404**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

<a id="opIddownloadReport"></a>

## GET 下载报告

GET /sast/reports/{reportId}/download

下载指定报告的文件

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|reportId|path|integer(int64)| 是 |报告ID|
|format|query|string| 否 |下载格式|
|Authorization|header|string| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|format|pdf|
|format|html|
|format|docx|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"downloadUrl":"https://api.example.com/downloads/report-20240312.pdf","fileName":"漏洞测试分析报告-20240312.pdf","fileSize":2048576}}
```

> 404 Response

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|报告下载成功|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|报告不存在|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||none|
|» msg|string|false|none||none|
|» data|object|false|none||none|
|»» downloadUrl|string|false|none||下载链接|
|»» fileName|string|false|none||文件名|
|»» fileSize|integer(int64)|false|none||文件大小（字节）|

状态码 **404**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

状态码 **500**

*错误响应*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||错误码|
|» msg|string|true|none||错误消息|
|» data|object¦null|false|none||错误详情|

# 数据模型

<h2 id="tocS_TaskSummary">TaskSummary</h2>

<a id="schematasksummary"></a>
<a id="schema_TaskSummary"></a>
<a id="tocStasksummary"></a>
<a id="tocstasksummary"></a>

```json
{
  "totalTasks": 265,
  "highRisk": 140,
  "highRiskPercent": 30,
  "mediumRisk": 109,
  "mediumRiskPercent": 30,
  "lowRisk": 87,
  "lowRiskPercent": 40
}

```

任务统计汇总

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|totalTasks|integer|false|none||测试总数|
|highRisk|integer|false|none||高危漏洞总数|
|highRiskPercent|integer|false|none||高危漏洞占比(%)|
|mediumRisk|integer|false|none||中危漏洞总数|
|mediumRiskPercent|integer|false|none||中危漏洞占比(%)|
|lowRisk|integer|false|none||低危漏洞总数|
|lowRiskPercent|integer|false|none||低危漏洞占比(%)|

<h2 id="tocS_VulnerabilityStats">VulnerabilityStats</h2>

<a id="schemavulnerabilitystats"></a>
<a id="schema_VulnerabilityStats"></a>
<a id="tocSvulnerabilitystats"></a>
<a id="tocsvulnerabilitystats"></a>

```json
{
  "highRisk": 100,
  "mediumRisk": 467,
  "lowRisk": 379
}

```

漏洞统计

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|highRisk|integer|false|none||高危漏洞数量|
|mediumRisk|integer|false|none||中危漏洞数量|
|lowRisk|integer|false|none||低危漏洞数量|

<h2 id="tocS_ErrorResponse">ErrorResponse</h2>

<a id="schemaerrorresponse"></a>
<a id="schema_ErrorResponse"></a>
<a id="tocSerrorresponse"></a>
<a id="tocserrorresponse"></a>

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}

```

错误响应

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|true|none||错误码|
|msg|string|true|none||错误消息|
|data|object¦null|false|none||错误详情|

<h2 id="tocS_FileUploadStatus">FileUploadStatus</h2>

<a id="schemafileuploadstatus"></a>
<a id="schema_FileUploadStatus"></a>
<a id="tocSfileuploadstatus"></a>
<a id="tocsfileuploadstatus"></a>

```json
{
  "sourceFileProgress": 100,
  "configFileProgress": 100
}

```

文件上传状态

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|sourceFileProgress|integer|false|none||源文件上传进度(%)|
|configFileProgress|integer|false|none||环境配置文件上传进度(%)|

<h2 id="tocS_AnalysisResults">AnalysisResults</h2>

<a id="schemaanalysisresults"></a>
<a id="schema_AnalysisResults"></a>
<a id="tocSanalysisresults"></a>
<a id="tocsanalysisresults"></a>

```json
{
  "highRisk": 12,
  "mediumRisk": 8,
  "lowRisk": 5
}

```

分析结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|highRisk|integer|false|none||高危漏洞数量|
|mediumRisk|integer|false|none||中危漏洞数量|
|lowRisk|integer|false|none||低危漏洞数量|

