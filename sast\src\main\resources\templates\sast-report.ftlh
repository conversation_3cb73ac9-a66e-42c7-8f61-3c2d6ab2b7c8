<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>源码测试分析报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #1e88e5;
            color: white;
            padding: 40px;
            position: relative;
            min-height: 200px;
        }
        .header-content {
            max-width: 800px;
        }
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
        }
        .download-btn {
            display: inline-block;
            background-color: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            border: 1px solid white;
        }
        .download-btn:hover {
            background-color: rgba(255,255,255,0.3);
        }
        .info-list {
            margin-top: 30px;
            line-height: 1.8;
        }
        .robot-image {
            position: absolute;
            right: 50px;
            bottom: 0;
            width: 200px;
        }
        .content {
            padding: 20px 40px;
        }
        .section-title {
            color: #1e88e5;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        .overview-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .overview-table th, .overview-table td {
            border: 1px solid #eee;
            padding: 12px;
            text-align: center;
        }
        .overview-table th {
            background-color: #f5f5f5;
        }
        .high-risk {
            background-color: #ff5252;
            color: white;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            display: inline-block;
        }
        .medium-risk {
            background-color: #ff9800;
            color: white;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            display: inline-block;
        }
        .low-risk {
            background-color: #ffeb3b;
            color: #333;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            display: inline-block;
        }
        .total-row {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .details-section {
            margin-top: 30px;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        .defect-name {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .defect-info {
            margin: 10px 0;
            display: flex;
            gap: 40px;
        }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: Consolas, monospace;
            overflow-x: auto;
        }
        .code-line {
            white-space: pre;
            line-height: 1.5;
        }
        .keyword {
            color: #0033b3;
        }
        .string {
            color: #008000;
        }
        .function {
            color: #7d5bbf;
        }
        .comment {
            color: #808080;
        }
        .reason-box {
            background-color: #fff8f8;
            border-left: 4px solid #ff5252;
            padding: 15px;
            margin: 15px 0;
        }
        .poc-section {
            margin-top: 30px;
        }
        .poc-code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: Consolas, monospace;
            overflow-x: auto;
            line-height: 1.5;
        }
        .python-keyword {
            color: #0033b3;
        }
        .python-string {
            color: #008000;
        }
        .python-comment {
            color: #808080;
        }
        .python-function {
            color: #7d5bbf;
        }
        .fix-suggestion {
            margin-top: 30px;
        }
        .fix-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .fix-desc {
            margin-bottom: 10px;
        }
        .fix-method {
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="title">源码测试分析报告</div>
                <a href="#" class="download-btn">↓ 下载报告</a>
                <div class="info-list">
                    所属项目：${projectName}<br>
                    所属任务：${taskName}<br>
                    所属单位：${department}<br>
                    报告生成时间：${reportTime}
                </div>
            </div>
            <img class="robot-image" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48cGF0aCBmaWxsPSIjZjhmOWZhIiBkPSJNMTI4IDI0YzU3LjQgMCAxMDQgNDYuNiAxMDQgMTA0cy00Ni42IDEwNC0xMDQgMTA0UzI0IDE4NS40IDI0IDEyOFM3MC42IDI0IDEyOCAyNHoiLz48cGF0aCBmaWxsPSIjMWU4OGU1IiBkPSJNMTI4IDU2Yy0zOS44IDAtNzIgMzIuMi03MiA3MnMzMi4yIDcyIDcyIDcyczcyLTMyLjIgNzItNzJzLTMyLjItNzItNzItNzJ6bTAgMTIwYy0yNi41IDAtNDgtMjEuNS00OC00OHMyMS41LTQ4IDQ4LTQ4IDQ4IDIxLjUgNDggNDgtMjEuNSA0OC00OCA0OHoiLz48Y2lyY2xlIGZpbGw9IiNmZmYiIGN4PSIxMjgiIGN5PSIxMjgiIHI9IjI0Ii8+PC9zdmc+" alt="Robot">
        </div>
        
        <div class="content">
            <div class="section-title">漏洞概览</div>
            <table class="overview-table">
                <tr>
                    <th>漏洞等级</th>
                    <th>漏洞数量</th>
                    <th>漏洞占比</th>
                </tr>
                <tr>
                    <td><span class="high-risk">高危</span></td>
                    <td>${highRiskCount}</td>
                    <td>${highRiskPercent}</td>
                </tr>
                <tr>
                    <td><span class="medium-risk">中危</span></td>
                    <td>${mediumRiskCount}</td>
                    <td>${mediumRiskPercent}</td>
                </tr>
                <tr>
                    <td><span class="low-risk">低危</span></td>
                    <td>${lowRiskCount}</td>
                    <td>${lowRiskPercent}</td>
                </tr>
                <tr class="total-row">
                    <td>总计</td>
                    <td>${totalCount}</td>
                    <td>100%</td>
                </tr>
            </table>
            
            <div class="section-title">漏洞详情</div>
            <#list defects as defect>
            <div class="details-section">
                <div class="defect-name">
                    <span class="${defect.levelClass}" style="margin-right:10px;">${defect.levelName}</span>
                    <strong>缺陷名称: ${defect.name}</strong>
                </div>
                <div class="defect-info">
                    <div><strong>漏洞类型:</strong> ${defect.type}</div>
                    <div><strong>漏洞缺陷行号:</strong> ${defect.lineNumber}</div>
                </div>
                <div>
                    <strong>代码片段:</strong>
                    <div class="code-block">
                        <#list defect.codeLines as line>
                        <div class="code-line">${line}</div>
                        </#list>
                    </div>
                </div>
                
                <div class="reason-box">
                    <strong>漏洞检出原因:</strong>
                    <p>${defect.reason}</p>
                </div>
                
                <#if defect.pocScript??>
                <div class="poc-section">
                    <strong>POC脚本:</strong>
                    <div class="poc-code">
                        <#list defect.pocScript as line>
                        <div class="code-line">${line}</div>
                        </#list>
                    </div>
                </div>
                </#if>
                
                <#if defect.fixSuggestion??>
                <div class="fix-suggestion">
                    <div class="fix-title">${defect.fixSuggestion.title}</div>
                    <div class="fix-desc">${defect.fixSuggestion.description}</div>
                    <div class="fix-method">修复方式：</div>
                    <div>${defect.fixSuggestion.method}</div>
                </div>
                </#if>
            </div>
            </#list>
        </div>
    </div>
</body>
</html> 