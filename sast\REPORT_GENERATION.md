# SAST 报告生成功能说明

## 功能概述

SAST模块提供了完整的报告生成功能，支持生成PDF和HTML格式的安全分析报告。报告包含漏洞概览、详细信息、代码片段等内容。

## 技术栈

- **OpenPDF 1.3.30**: 用于生成PDF报告
- **Spring Boot 3.x**: 后端框架
- **Java 17**: 开发语言
- **PostgreSQL**: 数据库

## 功能特性

### 1. 报告生成
- 支持PDF和HTML两种格式
- 自动统计漏洞数量和分布
- 包含项目、任务、时间等基本信息
- 支持中文字体显示

### 2. 报告内容
- **报告标题**: 源码测试分析报告
- **基本信息**: 项目名称、任务名称、所属单位、生成时间
- **漏洞概览**: 高危、中危、低危漏洞数量和占比
- **漏洞详情**: 缺陷名称、类型、行号、代码片段

### 3. 文件管理
- 自动创建输出目录
- 文件命名规则: `SAST_报告_{报告名称}_{时间戳}.{格式}`
- 支持文件重新生成
- 配置化的输出目录

## API接口

### 1. 生成报告
```http
POST /sast/reports/generate
Content-Type: application/json

{
  "reportName": "漏洞挖掘测试报告",
  "projectId": "1",
  "taskId": "1"
}
```

### 2. 下载PDF报告
```http
GET /sast/reports/download/pdf/{reportId}
```

### 3. 预览HTML报告
```http
GET /sast/reports/download/html/{reportId}
```

### 4. 重新生成报告
```http
POST /sast/reports/download/regenerate/{reportId}
```

## 配置说明

### 1. 应用配置
在 `application-sast.yml` 中配置：

```yaml
sast:
  report:
    output-dir: ./reports/sast
    retention-days: 30
    enable-cache: true
```

### 2. Maven依赖
在 `pom.xml` 中已配置：

```xml
<dependency>
    <groupId>com.github.librepdf</groupId>
    <artifactId>openpdf</artifactId>
    <version>1.3.30</version>
</dependency>
```

## 使用示例

### 1. 生成报告
```java
// 创建报告请求
GenerateReportRequest request = new GenerateReportRequest();
request.setReportName("测试报告");
request.setProjectId("1");
request.setTaskId("1");

// 调用服务
R<Void> result = reportManagementService.generateReport(request);
```

### 2. 下载报告
```java
// 下载PDF报告
String pdfPath = pdfReportService.generatePdfReport(report);

// 生成HTML报告
String htmlPath = pdfReportService.generateHtmlReport(report);
```

## 文件结构

```
sast/
├── src/main/java/com/cqcdi/ngsoc/sast/
│   ├── service/
│   │   ├── SastPdfReportService.java              # PDF报告服务接口
│   │   └── impl/
│   │       └── SastPdfReportServiceImpl.java      # PDF报告服务实现
│   └── controller/
│       └── SastReportDownloadController.java      # 报告下载控制器
├── src/main/resources/
│   └── application-sast.yml                       # SAST模块配置
└── src/test/java/
    └── SastPdfReportServiceTest.java              # 测试类
```

## 开发规范

### 1. 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理
- 完整的日志记录

### 2. 文件命名
- 报告文件: `SAST_报告_{报告名称}_{yyyyMMdd_HHmmss}.{格式}`
- 类文件: 使用大驼峰命名法
- 方法文件: 使用小驼峰命名法

### 3. 异常处理
- 使用统一的R<T>响应格式
- 记录详细的错误日志
- 提供友好的错误信息

## 注意事项

1. **字体支持**: 确保系统支持中文字体显示
2. **文件权限**: 确保应用有写入输出目录的权限
3. **磁盘空间**: 定期清理过期的报告文件
4. **并发安全**: 多用户同时生成报告时的文件命名冲突处理

## 故障排除

### 1. PDF生成失败
- 检查OpenPDF依赖是否正确配置
- 确认输出目录存在且有写入权限
- 查看日志中的详细错误信息

### 2. 中文显示问题
- 确认使用正确的中文字体
- 检查文件编码是否为UTF-8
- 验证字体文件是否可用

### 3. 文件下载失败
- 检查文件是否存在
- 确认文件路径正确
- 验证HTTP响应头设置

## 扩展功能

### 1. 报告模板
- 支持自定义报告模板
- 可配置的报告样式
- 支持多种报告格式

### 2. 报告缓存
- 实现报告文件缓存机制
- 减少重复生成的开销
- 支持缓存清理策略

### 3. 批量生成
- 支持批量生成报告
- 异步处理大量报告
- 进度跟踪和状态反馈 