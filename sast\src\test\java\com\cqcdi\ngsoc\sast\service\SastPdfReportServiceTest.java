/*
 * Copyright @ 2024 重庆市信息通信咨询设计院有限公司版权所有
 *
 * 项目名称: ngsoc-api
 * 文件名称: SastPdfReportServiceTest.java
 *
 * 创建人: Weilq
 * 创建日期: 2025/7/31
 *
 * 版权描述:此软件未经重庆市信息通信咨询设计院有限公司许可，严禁发布、传播、使用
 * 公司地址:重庆市九龙坡区科园四路257号，400041.
 */
package com.cqcdi.ngsoc.sast.service;

import com.cqcdi.ngsoc.common.entity.common.dto.R;
import com.cqcdi.ngsoc.common.entity.sast.SastReportManagement;
import com.cqcdi.ngsoc.sast.service.impl.SastPdfReportServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SAST PDF报告生成服务测试类
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@ExtendWith(MockitoExtension.class)
class SastPdfReportServiceTest {

    @InjectMocks
    private SastPdfReportServiceImpl pdfReportService;

    @Mock
    private com.cqcdi.ngsoc.sast.repo.pg.SastProjectManagementRepo projectRepository;

    @Mock
    private com.cqcdi.ngsoc.sast.repo.pg.SastTaskManagementRepo taskRepository;
    @Mock
    private com.cqcdi.ngsoc.sast.repo.pg.SastReportManagementRepo reportRepository;

    private SastReportManagement testReport;

    @BeforeEach
    void setUp() {
        // 设置测试输出目录
        ReflectionTestUtils.setField(pdfReportService, "reportOutputDir", "./test-reports");

        // 创建测试报告数据
        testReport = new SastReportManagement();
        testReport.setId(1L);
        testReport.setReportName("测试报告");
        testReport.setProjectId(1L);
        testReport.setTaskId(1L);
        testReport.setTotalDefectCount(100);
        testReport.setHighRiskCount(30);
        testReport.setMediumRiskCount(50);
        testReport.setLowRiskCount(20);
        testReport.setVerificationSuccessCount(80);
        testReport.setVerificationFailureCount(20);
        testReport.setReportGenerationTime(LocalDateTime.now());
        testReport.setCreatedId(1L);
        testReport.setCreatedName("测试用户");
    }

    @Test
    void generatePdfReportByTemplate() {
        Optional<SastReportManagement> reportOpt = reportRepository.findById(2L);
         if (reportOpt.isEmpty()) {
             return;
        }
        SastReportManagement report = reportOpt.get();
        pdfReportService.generateHtmlReportWithTemplate(report);
    }

} 