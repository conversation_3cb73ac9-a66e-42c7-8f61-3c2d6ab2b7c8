################################################### 项目基本配置 #########################################################
server:
  port: 21001

# 不添加api前缀controller集合
#excludeApiController: com.cqcdi.ngsoc.auth.**
excludeApiController:

spring:
  application:
    # 注册到Eureka的服务名
    name: ngsoc
  cache:
    type: simple
  main:
    allow-circular-references: true
  datasource:
    # 主数据库, 优先加载
    primary:
      driver-class-name: org.postgresql.Driver
      username: postgres
      password: cqcdi@123456
      url: ************************************************************************************************************************************
    # doris数据库
    secondary:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: root
      password: cqcdi@123456
      url: ****************************************************************************************************************************************************
      hikari:
        # 默认最大10
        maximumPoolSize: 10
    # doris数据库
    thired:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: root
      password: cqcdi@123456
      url: ****************************************************************************************************************************************
      hikari:
        # 默认最大10
        maximumPoolSize: 10
  jpa:
    primary:
      show-sql: false
      properties:
        hibernate:
          hbm2ddl:
            auto: none
          dialect: org.hibernate.dialect.PostgreSQLDialect
          format_sql: false
    secondary:
      show-sql: false
      properties:
        hibernate:
          hbm2ddl:
            auto: none
          dialect: org.hibernate.dialect.MySQLDialect
          format_sql: true
  #kafka配置
  kafka:
    bootstrap-servers: http://192.168.0.245:9092
    #      producer:
    #        # 发生错误后,消息重发的次数
    #        retries: 0
    #        # 键的序列化方式
    #        key-serializer: org.apache.kafka.common.serialization.StringSerializer
    #        # 值的序列化方式
    #        value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: test-consumer-group
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      # 当消费者监听的topic不存在时，保证项目能够启动。
      missing-topics-fatal: false
  data:
    # redis配置
    redis:
      # Redis数据库索引（默认为0）
      database: 0
      # Redis服务器地址
      host: *************
      # Redis服务器连接端口
      port: 6379
      # Redis服务器连接密码（默认为空）
      password: cqcdi@123456
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 200
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 0
  # 上传文件大小限制
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
      file-size-threshold: 500MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-date-keys-as-timestamps: false

order:
  url: http://*************:17777/flow-service-plus/workflow/processInfoListByBusinessKeys

terminal:
  url: https://************
  appKey: 9ad348e13c3604a971e5a40ceed4e8dd
  appSecret: 8355f65e8909cf6546a109f131e2e7e76a794bdf
  appName: ywglpt
  iv: 86Tn07T254x68EZb

################################################### minio基本配置 ########################################################
minio:
  overwrite: true  #是否覆盖minio的文件
  endpoint: http://*************:23131 #Minio服务所在地址
  bucketName: next-soc #存储桶名称
  accessKey: cqcdi_cqcdi #访问的key
  secretKey: 'cqcdi*2023' #访问的秘钥
  directory: 'sca/sca-resources/'
  exportDir: 'sca/sca-export/'
  daily:
    uploadFile: 'daily/uploadFile/' #轨道单独模块，日常管理文件上传路径
    exportTemplate: 'daily/exportTemplate/' #轨道单独模块，日常管理文件模版路径
  threat:
    uploadFile: 'threat/uploadFile/' #轨道单独模块，威胁文件上传路径
  importPath:
    excel: '/workbench/file/'  #导入模板文件的新存储路径，统一管理

redirect:
  login: 'http://*************:5173/auth/login'


endpoints:
  enabled: false

################################################### assets基本配置 #######################################################



################################################### auth基本配置 #########################################################
# Sa-Token配置
sa-token:
  # token 名称 (同时也是 cookie 名称)
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效（交由auth模块进行配置控制）
  timeout: 360000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz
  # 允许动态设置 token 有效期
  dynamic-active-timeout: true
  # 允许从 请求参数 读取 token
  is-read-body: true
  # 允许从 header 读取 token
  is-read-header: true
  # 关闭 cookie 鉴权 从根源杜绝 csrf 漏洞风险
  is-read-cookie: false
  # token前缀
  token-prefix: "Bearer"

# 安全配置
security:
  # 验证码
  captcha:
    # 验证码类型 math 数组计算 char 字符验证
    type: MATH
    # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
    category: SHEAR
    # 数字验证码位数
    numberLength: 1
    # 字符验证码长度
    charLength: 4
  encryption:
    master-key: "tZ4QkX9v7gYJ2nL6WpBcH8sD1fR3mNqP"

################################################### notice基本配置 ######################################################
notice:
  copy-scene-code: notice_publish_copy
  receive-scene-code: notice_publish_receive
  url: http://*************:21001/
# 通知通报任务线程池核心线程数大小
noticeTask:
  # 核心线程数大小
  coreSize: 10
################################################### threat基本配置 #######################################################


################################################### sca基本配置 ##########################################################
sca:
  scanner-url: "http://*************:17070/"
  scan-callback-url: "http://*************:21001/detect/scanCallBack"
  scene-code: sca_check_notice

################################################### workbench基本配置 ####################################################
xxl:
  job:
    accessToken: default_token
    admin:
      addresses: http://*************:8061/xxl-job-admin
    executor:
      appname: notice-executor
      address:
      ip: *************
      port: 9999
      logpath:
      logretentiondays: 30


################################################### openai基本配置 ####################################################
# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true    #开启knife4j,无需添加@EnableKnife4j注解
  setting:
    language: zh_cn   #中文
    swagger-model-name: 实体列表   #默认为：Swagger Models
  #开启Swagger的Basic认证功能,默认是false,开启后访问文档页面时会弹出用户名和密码输入框
  basic:
    enable: false
    # Basic认证用户名
    username: user
    # Basic认证密码
    password: 123456

springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

# http请求重试配置
retry:
  enabled: false
  maxRetries: 3
  delay: 1000

logging:
  level:
    org.apache.kafka.clients.NetworkClient: ERROR  # 可以更细粒度地控制特定类的日志级别
    root: WARN # 设置全局日志级别为 WARN
    org.springframework: ERROR # Spring 框架的日志级别为 ERROR
    com.cqcdi: INFO # 项目包的日志级别为 WARN


################################################### knowledge基本配置 ####################################################
## minio配置
knowledge-minio:
  endpoint: http://*************:9000
  viewEndpoint: http://*************:9009
  access-key: cqcdi_cqcdi
  secret-key: cqcdi*2023
  bucket: public-knowledge

mainplatform:
  api:
    url: http://*************:21001

oos:
  rule:
    checkOos: true
    checkOwn: false
  login:
    auto: false
  token:
    needCheck: true
  local:
    use:
      own: false
      oosToken: true

################################################### vuln基本配置 ####################################################
workflowsystem:
  processDefinitionKey: DEF_1734957915808PZSCQT
  executeUserId: 1
  systemCode: NEXT_SOC
  url: http://*************:17777

dolphinScheduler:
  server:
    url: http://*************:12345
  token:
    value: 96c73bc7353c5c4e7edeb0595a218ada
  task:
    projectcode: 15699304778464
    self:
      selfCode: 15709218222304
      version: 15
    allandself:
      allandselfCode: 15709271817824
      version: 16
    all:
      allCode: 15708744621664
      version: 39
    zhidingandself:
      zhidingandselfCode: 15709303985504
      version: 17
    zhiding:
      zhidingCode: 15709185774048
      version: 14
    assetchange:
      assetchangeCode: 15863762741216
      version: 12
    dimdwd:
      code: 15833145920746
      version: 36

scene-code-1: vuln_wait_deal
scene-code-2: vuln_orsign_copy
scene-code-3: vuln_revoke

#################################################### 漏洞扫描输出数据配置 ####################################################
vuln:
  scan:
    database: vuln_scan_v2     #  doris 数据库名称
kafka:
  topic: test-green-result  #  kafka topic 名称

################################################### 大屏基本配置 ####################################################
screen:
  retry: 3
threat:
  config_key: threat_safe_process_definition_key

################################################### 威胁系统基本配置 ####################################################
# dinky:
#   flink:
#     databaseid: 1


