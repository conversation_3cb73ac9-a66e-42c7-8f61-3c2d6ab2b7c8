/*
 * Copyright @ 2024 重庆市信息通信咨询设计院有限公司版权所有
 *
 * 项目名称: ngsoc-api
 * 文件名称: SastPdfReportService.java
 *
 * 创建人: Weilq
 * 创建日期: 2025/7/31
 *
 * 版权描述:此软件未经重庆市信息通信咨询设计院有限公司许可，严禁发布、传播、使用
 * 公司地址:重庆市九龙坡区科园四路257号，400041.
 */
package com.cqcdi.ngsoc.sast.service;

import com.cqcdi.ngsoc.common.entity.sast.SastReportManagement;
import java.util.Map;

/**
 * SAST PDF报告生成服务接口
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
public interface SastPdfReportService {


    /**
     * 生成HTML报告文件（用于预览）
     *
     * @param report 报告管理实体
     * @return HTML文件路径
     */
    String generateHtmlReportWithTemplate(SastReportManagement report);
    
    /**
     * 准备报告模板数据
     * 
     * @param report 报告管理实体
     * @return 模板数据Map
     */
    Map<String, Object> prepareTemplateData(SastReportManagement report);
} 