/*
 * Copyright @ 2024 重庆市信息通信咨询设计院有限公司版权所有
 *
 * 项目名称: ngsoc-api
 * 文件名称: SastDefectInfoRepo.java
 *
 * 创建人: Weilq
 * 创建日期: 2025/7/31
 *
 * 版权描述:此软件未经重庆市信息通信咨询设计院有限公司许可，严禁发布、传播、使用
 * 公司地址:重庆市九龙坡区科园四路257号，400041.
 */
package com.cqcdi.ngsoc.sast.repo.pg;

import com.cqcdi.ngsoc.common.entity.sast.SastDefectInfo;
import jakarta.persistence.Tuple;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * SAST缺陷信息Repository
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@Repository
public interface SastDefectInfoRepo extends JpaRepository<SastDefectInfo, Long> {

    /**
     * 统计项目下各风险等级漏洞数量
     */
    @Query("SELECT " +
           "COALESCE(SUM(CASE WHEN d.defectLevel = '高危' THEN 1 ELSE 0 END), 0) as high_risk, " +
           "COALESCE(SUM(CASE WHEN d.defectLevel = '中危' THEN 1 ELSE 0 END), 0) as medium_risk, " +
           "COALESCE(SUM(CASE WHEN d.defectLevel = '低危' THEN 1 ELSE 0 END), 0) as low_risk " +
           "FROM SastDefectInfo d WHERE d.projectId = :projectId AND d.delFlag = 0")
    Tuple countVulnerabilityStatsByProjectId(@Param("projectId") Long projectId);

    /**
     * 统计任务下各风险等级漏洞数量
     */
    @Query("SELECT " +
           "COALESCE(SUM(CASE WHEN d.defectLevel = '高危' THEN 1 ELSE 0 END), 0) as high_risk, " +
           "COALESCE(SUM(CASE WHEN d.defectLevel = '中危' THEN 1 ELSE 0 END), 0) as medium_risk, " +
           "COALESCE(SUM(CASE WHEN d.defectLevel = '低危' THEN 1 ELSE 0 END), 0) as low_risk " +
           "FROM SastDefectInfo d WHERE d.taskId = :taskId AND d.delFlag = 0")
    Tuple countVulnerabilityStatsByTaskId(@Param("taskId") Long taskId);

    /**
     * 统计项目下总漏洞数量
     */
    @Query("SELECT COUNT(d) FROM SastDefectInfo d WHERE d.projectId = :projectId AND d.delFlag = 0")
    Long countByProjectId(@Param("projectId") Long projectId);

    /**
     * 统计任务下总漏洞数量
     */
    @Query("SELECT COUNT(d) FROM SastDefectInfo d WHERE d.taskId = :taskId AND d.delFlag = 0")
    Long countByTaskId(@Param("taskId") Long taskId);

    /**
     * 统计验证结果数量
     */
    @Query("SELECT " +
           "COALESCE(SUM(CASE WHEN d.defectVerificationResult = '验证成功' THEN 1 ELSE 0 END), 0) as verification_success, " +
           "COALESCE(SUM(CASE WHEN d.defectVerificationResult = '验证失败' THEN 1 ELSE 0 END), 0) as verification_fail " +
           "FROM SastDefectInfo d WHERE (:taskId IS NULL OR d.taskId = :taskId) AND d.delFlag = 0")
    Tuple countVerificationResults(@Param("taskId") Long taskId);

    /**
     * 统计全局验证结果数量
     */
    @Query("SELECT " +
           "COALESCE(SUM(CASE WHEN d.defectVerificationResult = '验证成功' THEN 1 ELSE 0 END), 0) as verification_success, " +
           "COALESCE(SUM(CASE WHEN d.defectVerificationResult = '验证失败' THEN 1 ELSE 0 END), 0) as verification_fail " +
           "FROM SastDefectInfo d WHERE d.delFlag = 0")
    Tuple countAllVerificationResults();

    /**
     * 统计全局各风险等级漏洞数量
     */
    @Query("SELECT " +
           "COALESCE(SUM(CASE WHEN d.defectLevel = '高危' THEN 1 ELSE 0 END), 0) as high_risk, " +
           "COALESCE(SUM(CASE WHEN d.defectLevel = '中危' THEN 1 ELSE 0 END), 0) as medium_risk, " +
           "COALESCE(SUM(CASE WHEN d.defectLevel = '低危' THEN 1 ELSE 0 END), 0) as low_risk " +
           "FROM SastDefectInfo d WHERE d.delFlag = 0")
    Tuple countAllVulnerabilityStats();

    /**
     * 统计全局总漏洞数量
     */
    @Query("SELECT COUNT(d) FROM SastDefectInfo d WHERE d.delFlag = 0")
    Long countAll();



}
