-- auto-generated definition
create table app_sast_project_management
(
    id                  bigint       default snow_next_id_bigint()         not null
        primary key,
    project_name        varchar(255)                                       not null,
    project_description varchar(255) default '项目描述'                     not null,
    created_id          bigint                                             not null,
    created_name        varchar(50)                                        not null,
    created_time        timestamp    default now(),
    modify_id           bigint,
    modify_name         varchar(50),
    modify_time         timestamp    default now(),
    del_flag            smallint     default '0'::smallint                 not null

);

comment on table app_sast_project_management is 'SAST项目管理表';

comment on column app_sast_project_management.id is '自增主键ID';

comment on column app_sast_project_management.project_name is '项目名称';

comment on column app_sast_project_management.created_id is '创建人标识';

comment on column app_sast_project_management.created_name is '创建人';

comment on column app_sast_project_management.created_time is '创建时间';

comment on column app_sast_project_management.modify_id is '更新人标识';

comment on column app_sast_project_management.modify_name is '更新人';

comment on column app_sast_project_management.modify_time is '更新时间';

comment on column app_sast_project_management.del_flag is '删除标识(0:未删除 1:已删除)';



alter table app_sast_project_management
    owner to postgres;

create index idx_app_sast_project_management_create_time
    on app_sast_project_management (created_time);


-- auto-generated definition
create table app_sast_task_management
(
    task_id          bigint      default snow_next_id_bigint()     not null
        primary key,
    project_id       bigint                                        not null
    task_name        varchar(255)                                  not null,
    task_language    varchar(100)                                  not null,
    task_model_used  varchar(255)                                  not null,
    analysis_method  varchar(50)                                   not null,
    build_method     varchar(50)                                   not null,
    task_description text,
    task_status      varchar(50) default '排队'::character varying not null,
    created_id       bigint                                        not null,
    created_name     varchar(50)                                   not null,
    created_time     timestamp   default now(),
    modify_id        bigint,
    modify_name      varchar(50),
    modify_time      timestamp   default now(),
    del_flag         smallint    default '0'::smallint             not null
);

comment on table app_sast_task_management is 'SAST任务管理表';

comment on column app_sast_task_management.task_id is '主键ID，自增';

comment on column app_sast_task_management.project_id is '所属项目ID，外键关联';

comment on column app_sast_task_management.task_name is '任务名称，必填';

comment on column app_sast_task_management.task_language is '编程语言，如 Java/Python/Go';

comment on column app_sast_task_management.task_model_used is '使用的大模型名称，如 GPT-4，可多个，逗号隔开';

comment on column app_sast_task_management.task_description is '任务描述，可空';

comment on column app_sast_task_management.task_status is '任务状态，如 排队/传播器查找/链路分析/研判/POC验证/修复建议生成';

comment on column app_sast_task_management.created_name is '创建人';

comment on column app_sast_task_management.created_time is '创建时间';

comment on column app_sast_task_management.modify_id is '更新人标识';

comment on column app_sast_task_management.modify_name is '更新人';

comment on column app_sast_task_management.modify_time is '更新时间';

comment on column app_sast_task_management.del_flag is '删除标识(0:未删除 1:已删除)';

comment on column app_sast_task_management.analysis_method is '在线分析或者离线分析';
comment on column app_sast_task_management.build_method is '构建方法,maven或者gradle';

alter table app_sast_task_management
    owner to postgres;

create index idx_app_sast_task_management_project_id
    on app_sast_task_management (project_id);

create index idx_app_sast_task_management_status
    on app_sast_task_management (task_status);

create index idx_app_sast_task_management_language
    on app_sast_task_management (task_language);

-- auto-generated definition
create table app_sast_task_files
(
    id           bigint       default snow_next_id_bigint() not null
        primary key,
    task_id      bigint                                     not null
    file_type    varchar(50)                                not null,
    file_name    varchar(255)                               not null,
    file_path    varchar(500)                               not null,
    file_size    bigint,
    file_hash    varchar(64),
    upload_time  timestamp(6) default CURRENT_TIMESTAMP(6),
    created_id   bigint                                     not null,
    created_name varchar(50)                                not null,
    created_time timestamp    default now(),
    modify_id    bigint,
    modify_name  varchar(50),
    modify_time  timestamp    default now(),
    del_flag     smallint     default 0                     not null
);


-- auto-generated definition
create table app_sast_model_config
(
    id               bigint        default snow_next_id_bigint()       not null
        primary key,
    model_name       varchar(255)                                      not null,
    supplier         varchar(255)                                      not null,
    model_identifier varchar(255)                                      not null,
    api_type         varchar(100)                                      not null,
    base_url         varchar(500)                                      not null,
    api_key          varchar(500)                                      not null,
    max_tokens       integer       default 4000                        not null,
    temperature      numeric(3, 2) default 0.7                         not null,
    timeout_setting  integer       default 30                          not null,
    extra_headers    jsonb,
    weight_setting   numeric(3, 2) default 0.5                         not null,
    test_status      varchar(50)   default '未测试'::character varying not null,
    created_id       bigint                                            not null,
    created_name     varchar(50)                                       not null,
    created_time     timestamp     default now(),
    modify_id        bigint,
    modify_name      varchar(50),
    modify_time      timestamp     default now(),
    del_flag         smallint      default 0                           not null
);

-- auto-generated definition
create table app_sast_defect_info
(
    id                         bigint      default snow_next_id_bigint() not null
        primary key,
    task_id                    bigint                                    not null
    project_id                 bigint                                    not null
    vulnerability_type         varchar(100)                              not null,
    defect_path                varchar(255)                              not null,
    defect_line                integer                                   not null,
    defect_level               varchar(50)                               not null,
    defect_verification_result varchar(50) default '未验证'::character varying,
    defect_fix_suggestion      text                                      not null,
    defect_chain_info          jsonb,
    defect_llm_results         jsonb,
    created_id                 bigint                                    not null,
    created_name               varchar(50)                               not null,
    created_time               timestamp   default now(),
    modify_id                  bigint,
    modify_name                varchar(50),
    modify_time                timestamp   default now(),
    del_flag                   smallint    default '0'::smallint         not null
);

comment on table app_sast_defect_info is 'SAST缺陷信息表';

comment on column app_sast_defect_info.id is '主键ID，自增';

comment on column app_sast_defect_info.task_id is '所属任务ID，外键关联';

comment on column app_sast_defect_info.project_id is '所属项目ID，外键关联';

comment on column app_sast_defect_info.vulnerability_type is '漏洞类型，如SQL注入/XSS/命令注入等';

comment on column app_sast_defect_info.defect_path is '缺陷路径，必填';

comment on column app_sast_defect_info.defect_line is '缺陷行号，必填';

comment on column app_sast_defect_info.defect_level is '危险程度：高危/中危/低危';

comment on column app_sast_defect_info.defect_verification_result is '验证结果：成功/失败/未验证';

comment on column app_sast_defect_info.defect_fix_suggestion is '修复建议，必填';

comment on column app_sast_defect_info.defect_chain_info is '整条链路信息，CodeQL链路数据';

comment on column app_sast_defect_info.defect_llm_results is '大模型检测结果，array[json]格式，多个大模型结果';

comment on column app_sast_defect_info.created_name is '创建人';

comment on column app_sast_defect_info.created_time is '创建时间';

comment on column app_sast_defect_info.modify_id is '更新人标识';

comment on column app_sast_defect_info.modify_name is '更新人';

comment on column app_sast_defect_info.modify_time is '更新时间';

comment on column app_sast_defect_info.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_defect_info
    owner to postgres;

create index idx_app_sast_defect_info_task_id
    on app_sast_defect_info (task_id);

create index idx_app_sast_defect_info_project_id
    on app_sast_defect_info (project_id);

create index idx_app_sast_defect_info_vulnerability_type
    on app_sast_defect_info (vulnerability_type);

create index idx_app_sast_defect_info_severity_level
    on app_sast_defect_info (defect_level);



comment on table app_sast_model_config is 'SAST大模型配置表';

comment on column app_sast_model_config.id is '主键ID';

comment on column app_sast_model_config.model_name is '模型名称，必填';

comment on column app_sast_model_config.supplier is '供应商，如OpenAI/Claude等';

comment on column app_sast_model_config.model_identifier is '模型标识，必填';

comment on column app_sast_model_config.api_type is 'API类型，如REST/WebSocket等';

comment on column app_sast_model_config.base_url is '基础URL，必填';

comment on column app_sast_model_config.api_key is 'API密钥，必填';

comment on column app_sast_model_config.max_tokens is '最大token数，默认4000';

comment on column app_sast_model_config.temperature is '输出温度(0.00-1.00)';

comment on column app_sast_model_config.timeout_setting is '超时设置(秒)，默认30';

comment on column app_sast_model_config.extra_headers is '额外的请求头，JSON格式';

comment on column app_sast_model_config.weight_setting is '权重设置(0.00-1.00)';

comment on column app_sast_model_config.test_status is '模型测试状态：正常/异常/未测试';

comment on column app_sast_model_config.created_id is '创建人ID';

comment on column app_sast_model_config.created_name is '创建人姓名';

comment on column app_sast_model_config.created_time is '创建时间';

comment on column app_sast_model_config.modify_id is '更新人ID';

comment on column app_sast_model_config.modify_name is '更新人姓名';

comment on column app_sast_model_config.modify_time is '更新时间';

comment on column app_sast_model_config.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_model_config
    owner to postgres;

create index idx_app_sast_model_config_supplier
    on app_sast_model_config (supplier);

create index idx_app_sast_model_config_test_status
    on app_sast_model_config (test_status);



comment on table app_sast_task_files is 'SAST任务文件存储表';

comment on column app_sast_task_files.id is '主键ID';

comment on column app_sast_task_files.task_id is '关联任务ID，外键关联';

comment on column app_sast_task_files.file_type is '文件类型：预编译压缩包/源码文件';

comment on column app_sast_task_files.file_name is '文件名，必填';

comment on column app_sast_task_files.file_path is '文件存储路径，必填';

comment on column app_sast_task_files.file_size is '文件大小(字节)';

comment on column app_sast_task_files.file_hash is '文件哈希值(MD5/SHA256)';

comment on column app_sast_task_files.upload_time is '上传时间，精确到微秒';

comment on column app_sast_task_files.created_id is '创建人ID';

comment on column app_sast_task_files.created_name is '创建人姓名';

comment on column app_sast_task_files.created_time is '创建时间';

comment on column app_sast_task_files.modify_id is '更新人ID';

comment on column app_sast_task_files.modify_name is '更新人姓名';

comment on column app_sast_task_files.modify_time is '更新时间';

comment on column app_sast_task_files.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_task_files
    owner to postgres;

create index idx_app_sast_task_files_task_id
    on app_sast_task_files (task_id);

create index idx_app_sast_task_files_file_type
    on app_sast_task_files (file_type);



-- auto-generated definition
create table app_sast_task_stages
(
    id                 bigint    default snow_next_id_bigint() not null
        primary key,
    task_id            bigint                                  not null
    stage_name         varchar(100)                            not null,
    stage_status       varchar(50)                             not null,
    start_time         timestamp(6),
    end_time           timestamp(6),
    stage_order        integer                                 not null,
    token              integer,
    vulnerability_type varchar(100),
    created_id         bigint                                  not null,
    created_name       varchar(50)                             not null,
    created_time       timestamp default now(),
    modify_id          bigint,
    modify_name        varchar(50),
    modify_time        timestamp default now(),
    del_flag           smallint  default 0                     not null
);

comment on table app_sast_task_stages is 'SAST任务执行阶段表';

comment on column app_sast_task_stages.id is '主键ID';

comment on column app_sast_task_stages.task_id is '关联任务ID，外键关联';

comment on column app_sast_task_stages.stage_name is '阶段名称，必填';

comment on column app_sast_task_stages.stage_status is '阶段状态：进行中/已完成/失败';

comment on column app_sast_task_stages.start_time is '开始时间，精确到微秒';

comment on column app_sast_task_stages.end_time is '结束时间，精确到微秒';

comment on column app_sast_task_stages.stage_order is '阶段顺序，必填';

comment on column app_sast_task_stages.token is 'token消耗数，未统计填-1';

comment on column app_sast_task_stages.vulnerability_type is '漏洞类型，某些阶段与特定漏洞类型相关';

comment on column app_sast_task_stages.created_id is '创建人ID';

comment on column app_sast_task_stages.created_name is '创建人姓名';

comment on column app_sast_task_stages.created_time is '创建时间';

comment on column app_sast_task_stages.modify_id is '更新人ID';

comment on column app_sast_task_stages.modify_name is '更新人姓名';

comment on column app_sast_task_stages.modify_time is '更新时间';

comment on column app_sast_task_stages.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_task_stages
    owner to postgres;

create index idx_app_sast_task_stages_task_id
    on app_sast_task_stages (task_id);

create index idx_app_sast_task_stages_stage_status
    on app_sast_task_stages (stage_status);

create index idx_app_sast_task_stages_stage_order
    on app_sast_task_stages (stage_order);

-- auto-generated definition
create table app_sast_token_statistics
(
    id                 bigint         default snow_next_id_bigint()    not null
        primary key,
    task_id            bigint                                          not null
    stage_id           bigint                                          not null
    model_id           bigint                                          not null
    vulnerability_type varchar(100),
    stage_type         varchar(100)                                    not null,
    input_tokens       integer        default 0                        not null,
    output_tokens      integer        default 0                        not null,
    total_tokens       integer        default 0                        not null,
    cost_amount        numeric(10, 6) default 0                        not null,
    currency           varchar(10)    default 'USD'::character varying not null,
    created_id         bigint                                          not null,
    created_name       varchar(50)                                     not null,
    created_time       timestamp      default now(),
    modify_id          bigint,
    modify_name        varchar(50),
    modify_time        timestamp      default now(),
    del_flag           smallint       default 0                        not null
);

comment on table app_sast_token_statistics is 'SAST Token使用统计表';

comment on column app_sast_token_statistics.id is '主键ID';

comment on column app_sast_token_statistics.task_id is '关联任务ID，外键关联';

comment on column app_sast_token_statistics.stage_id is '关联阶段ID，外键关联';

comment on column app_sast_token_statistics.model_id is '关联模型ID，外键关联';

comment on column app_sast_token_statistics.vulnerability_type is '漏洞类型，可空';

comment on column app_sast_token_statistics.stage_type is '阶段类型：外部函数分类/净化函数提取/漏洞研判等';

comment on column app_sast_token_statistics.input_tokens is '输入token数，默认0';

comment on column app_sast_token_statistics.output_tokens is '输出token数，默认0';

comment on column app_sast_token_statistics.total_tokens is '总token数，默认0';

comment on column app_sast_token_statistics.cost_amount is '费用金额，默认0';

comment on column app_sast_token_statistics.currency is '货币类型，默认USD';

comment on column app_sast_token_statistics.created_id is '创建人ID';

comment on column app_sast_token_statistics.created_name is '创建人姓名';

comment on column app_sast_token_statistics.created_time is '创建时间';

comment on column app_sast_token_statistics.modify_id is '更新人ID';

comment on column app_sast_token_statistics.modify_name is '更新人姓名';

comment on column app_sast_token_statistics.modify_time is '更新时间';

comment on column app_sast_token_statistics.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_token_statistics
    owner to postgres;

create index idx_app_sast_token_statistics_task_id
    on app_sast_token_statistics (task_id);

create index idx_app_sast_token_statistics_stage_id
    on app_sast_token_statistics (stage_id);

create index idx_app_sast_token_statistics_model_id
    on app_sast_token_statistics (model_id);

create index idx_app_sast_token_statistics_stage_type
    on app_sast_token_statistics (stage_type);

-- auto-generated definition
create table app_sast_poc_management
(
    id                   bigint       default snow_next_id_bigint() not null
        primary key,
    defect_id            bigint                                     not null
    poc_content          text                                       not null,
    verification_message text,
    generation_time      timestamp(6) default CURRENT_TIMESTAMP(6),
    verification_result  varchar(50)  default '未验证'::character varying,
    generation_model     varchar(255),
    created_id           bigint                                     not null,
    created_name         varchar(50)                                not null,
    created_time         timestamp    default now(),
    modify_id            bigint,
    modify_name          varchar(50),
    modify_time          timestamp    default now(),
    del_flag             smallint     default '0'::smallint         not null
);

comment on table app_sast_poc_management is 'SAST POC管理表';

comment on column app_sast_poc_management.id is '主键ID，自增';

comment on column app_sast_poc_management.defect_id is '关联缺陷ID，外键关联';

comment on column app_sast_poc_management.poc_content is 'POC内容，Nuclei YAML格式';

comment on column app_sast_poc_management.verification_message is '验证数据包，TXT格式';

comment on column app_sast_poc_management.generation_time is 'POC生成时间，精确到微秒';

comment on column app_sast_poc_management.verification_result is '验证结果：成功/失败/未验证';

comment on column app_sast_poc_management.generation_model is '生成POC的模型名称';

comment on column app_sast_poc_management.created_name is '创建人';

comment on column app_sast_poc_management.created_time is '创建时间';

comment on column app_sast_poc_management.modify_id is '更新人标识';

comment on column app_sast_poc_management.modify_name is '更新人';

comment on column app_sast_poc_management.modify_time is '更新时间';

comment on column app_sast_poc_management.del_flag is '删除标识(0:未删除 1:已删除)';

alter table app_sast_poc_management
    owner to postgres;

create index idx_app_sast_poc_management_defect_id
    on app_sast_poc_management (defect_id);

create index idx_app_sast_poc_management_verification_status
    on app_sast_poc_management (verification_result);

-- auto-generated definition
create table app_sast_report_management
(
    id                         bigserial
        primary key,
    report_name                varchar(255)        not null,
    project_id                 bigint              not null
        references app_sast_project_management,
    task_id                    bigint              not null
        references app_sast_task_management,
    report_generation_time     timestamp default now(),
    created_id                 bigint              not null,
    created_name               varchar(50)         not null,
    created_time               timestamp default now(),
    modify_id                  bigint,
    modify_name                varchar(50),
    modify_time                timestamp default now(),
    del_flag                   smallint  default 0 not null,
    total_defect_count         integer,
    high_risk_count            integer,
    medium_risk_count          integer,
    low_risk_count             integer,
    verification_success_count integer,
    verification_failure_count integer
);

comment on column app_sast_report_management.total_defect_count is '总缺陷数量，冗余字段';

comment on column app_sast_report_management.verification_success_count is '验证成功总数，冗余字段';

comment on column app_sast_report_management.verification_failure_count is '验证失败总数，冗余字段';

alter table app_sast_report_management
    owner to postgres;

create index idx_report_project
    on app_sast_report_management (project_id);

create index idx_report_task
    on app_sast_report_management (task_id);

create index idx_report_del_flag
    on app_sast_report_management (del_flag);

